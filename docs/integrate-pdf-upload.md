
PDF Processing Automation Plan

Overview

Implement an automated PDF upload and processing system that uses Gemini 2.5 
Flash API to extract business registration data and convert it to the 
structured JSON format defined in src/types/business-registration-types.ts.

Architecture

1. API Integration Layer

- OpenAI SDK Wrapper: Use OpenAI SDK as the base client for future LLM 
flexibility
- Gemini 2.5 Flash Integration: Custom adapter to interface with Google's 
Gemini API through OpenAI SDK patterns
- BYOK (Bring Your Own Key): Users provide their own Gemini API keys for 
processing

2. File Processing Pipeline

- PDF Upload Handler: Enhanced file upload component with validation
- PDF to Base64 Conversion: Convert PDF files for API transmission
- Prompt Engineering: Structured prompts to extract business registration data
- Response Parsing: Convert API responses to BusinessRegistrationReport type
- Error Handling: Comprehensive error handling and retry logic

3. Caching System

- LocalStorage Cache: Store processed results by PDF hash
- Cache Management: TTL, size limits, and manual cache clearing
- Offline Support: Access previously processed documents without re-processing

4. UI/UX Enhancements

- API Key Management: Secure input and storage of user API keys
- Processing Progress: Real-time status updates during PDF processing  
- Result Preview: Show extracted data before saving
- Error States: User-friendly error messages and retry options

Implementation Details

New Dependencies

{
  "openai": "^4.x.x",
  "pdf-parse": "^1.x.x", 
  "crypto-js": "^4.x.x"
}

New Components/Services

1. API Key Manager (src/services/api-key-manager.ts)
2. PDF Processor (src/services/pdf-processor.ts) 
3. Cache Manager (src/services/cache-manager.ts)
4. OpenRouter Client (src/services/openrouter-client.ts)
5. Upload Component (src/components/PdfUpload.tsx)
6. Settings Panel (src/components/SettingsPanel.tsx)

Enhanced Landing Page

- Replace mock upload with real PDF processing
- Add API key configuration modal
- Show processing progress with detailed steps
- Preview extracted data before navigation

Data Flow

1. User uploads PDF → File validation
2. Convert PDF to base64 → Generate file hash for caching
3. Check cache → If exists, load from localStorage
4. If not cached → Send to OpenRouter/Gemini API with structured prompt
5. Parse response → Validate against TypeScript schema
6. Cache result → Store in localStorage with metadata
7. Navigate to dashboard → Display processed data

Error Handling Strategy

- Network Issues: Automatic retry with exponential backoff
- API Failures: User-friendly error messages with troubleshooting tips
- Invalid PDFs: Clear feedback on supported formats/structures
- Parsing Errors: Fallback options and manual correction interface
- API Key Issues: Guided setup and validation flow

Security Considerations

- API keys stored securely in localStorage (encrypted)
- No server-side storage of sensitive data
- PDF content processed client-side only
- Cache data encrypted before localStorage storage

Performance Optimizations

- Chunked PDF processing for large files
- Background processing with Web Workers
- Progressive result display
- Intelligent caching strategy with size management
- Lazy loading of processed data components

This plan maintains the current demo functionality while adding robust PDF 
processing capabilities that scale for real-world usage.