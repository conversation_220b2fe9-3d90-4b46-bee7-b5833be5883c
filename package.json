{"name": "br-analysis", "private": true, "type": "module", "scripts": {"dev": "vite --port 3000", "start": "vite --port 3000", "build": "vite build && tsc", "serve": "vite preview", "preview": "vite preview", "test": "vitest run", "format": "biome format", "lint": "biome lint", "lint:fix": "biome lint --write --unsafe", "check": "biome check", "check:fix": "biome check --write --unsafe"}, "dependencies": {"@ai-sdk/openai": "^1.3.23", "@faker-js/faker": "^9.6.0", "@google/genai": "^1.11.0", "@openrouter/ai-sdk-provider": "^0.7.3", "@t3-oss/env-core": "^0.12.0", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-router": "^1.121.2", "@tanstack/react-router-devtools": "^1.121.2", "@tanstack/react-table": "^8.21.2", "@tanstack/router-plugin": "^1.121.2", "ai": "^4.3.19", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "lucide-react": "^0.476.0", "pdf-parse": "^1.1.1", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^3.1.0", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.14"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@types/crypto-js": "^4.2.2", "@types/pdf-parse": "^1.1.5", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "jsdom": "^26.0.0", "tailwindcss": "^3.4.17", "typescript": "^5.7.2", "vite": "^6.1.0", "vitest": "^3.0.5", "web-vitals": "^4.2.4"}}