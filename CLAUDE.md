# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
- `bun run dev` or `bun start` - Start development server on port 3000
- `bun run build` - Build for production (includes TypeScript compilation)
- `bun run preview` - Preview production build

### Code Quality
- `bun run lint` - Run Biome linter
- `bun run format` - Format code with Biome
- `bun run check` - Run Biome check (combines linting and formatting)
- `bun test` - Run Vitest tests

### Package Manager
This project uses **bun**. Use bun commands for installation and dependency management.

## Architecture Overview

### Tech Stack
- **Frontend**: React 19 with TypeScript
- **Routing**: TanStack Router (file-based routing)
- **Styling**: Tailwind CSS with shadcn/ui components
- **Charts**: Recharts for data visualization
- **Testing**: Vitest with Testing Library
- **Build Tool**: Vite
- **Code Quality**: Biome for linting/formatting

### Project Structure
```
src/
├── components/          # Reusable UI components
├── contexts/           # React contexts (e.g., ShareholderPopupContext)
├── data/               # Business registration data and schemas
├── lib/                # Utility functions
├── routes/             # TanStack Router file-based routes
├── types/              # TypeScript type definitions
└── styles.css          # Global styles
```

### Key Components
- **ShareholdingSection**: Complex component handling shareholding data visualization with pie charts, bar charts, and historical data
- **ClickableShareholderName**: Interactive component that triggers shareholder popups
- **ShareholderPopup**: Context-driven popup displaying detailed shareholder information
- **Business Registration Data**: Comprehensive data processing system with name change tracking and shareholding history

### Data Architecture
The application processes business registration data through:
- **Type Definitions**: Comprehensive TypeScript types in `types/business-registration-types.ts`
- **Data Processing**: Complex algorithms for tracking shareholding changes over time, including name changes and ownership transfers
- **Color Mapping**: Consistent color assignment for shareholders across all visualizations
- **Historical Tracking**: Comprehensive history of all business events and changes

### Context System
- **ShareholderPopupProvider**: Manages global state for shareholder popup interactions
- Used throughout the app for displaying detailed shareholder information on hover/click

### Routing
Uses TanStack Router with file-based routing:
- Routes defined in `src/routes/`
- Root layout in `__root.tsx` includes global providers
- Route generation handled automatically

### Styling Conventions
- Uses Tailwind CSS utility classes
- Components follow shadcn/ui patterns
- Consistent spacing and color schemes
- Responsive design with mobile-first approach

## Development Notes

### Code Style
- Biome configuration enforces tab indentation and double quotes
- TypeScript strict mode enabled
- Functional components with hooks preferred

### Data Processing
The business registration data system is complex and handles:
- Name changes over time with canonical name resolution
- Shareholding percentage calculations and normalization
- Historical timeline reconstruction from chronological events
- Spouse information extraction and display

### Component Patterns
- Props interfaces defined in `types/component-types.ts`
- Context providers wrap components at appropriate levels
- Consistent error handling and loading states
- Reusable utility functions in `lib/utils.ts`