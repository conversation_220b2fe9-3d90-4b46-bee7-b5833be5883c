import type {
	BusinessRegistrationReport,
	ShareholderInfo,
} from "../types/business-registration-types";
import idealData from "./ideal-data.json";

// Export the new schema types
export * from "../types/business-registration-types";

// Cast the imported JSON data to our typed interface
export const businessRegistrationReport: BusinessRegistrationReport =
	idealData as BusinessRegistrationReport;

// Helper functions to extract information from the real schema
export function getCompanyInfo() {
	const report = businessRegistrationReport.reportInformation;

	return {
		// Basic company information
		registration_number: report.registrationNumber,
		application_number: report.applicationNumber,
		current_name: {
			zh: report.companyName.chinese,
			pt: report.companyName.portuguese,
			en: report.companyName.english,
		},

		// Registry information
		registry: {
			zh: report.registry.chinese,
			pt: report.registry.portuguese,
		},
		report_title: {
			zh: report.reportTitle.chinese,
			pt: report.reportTitle.portuguese,
		},

		// Financial information
		current_capital: getCurrentCapital(),
		currency: getCurrentCapital() > 0 ? "MOP" : "MOP",

		// Dates
		report_as_of_date: report.informationAsOf,
		print_date: report.printDate,
		establishment_date: getEstablishmentDate(),

		// Location and business
		current_address: getCurrentAddress(),
		original_address: getOriginalAddress(),
		business_scope: getBusinessScope(),
		business_objects: getBusinessObjects(),

		// Statistics
		total_historical_events:
			businessRegistrationReport.inscriptionsAndEndorsements.length,
		total_shareholders_ever: getTotalShareholdersEver(),
		total_capital_changes: getCapitalChanges(),
		total_quota_transfers: getQuotaTransferCount(),
		total_administrator_changes: getAdministratorChangeCount(),
	};
}

export function getCurrentShareholders() {
	// Build name change mappings first
	const nameMap = buildNameChangeMappings();

	// Build current shareholding state from all quota transfers and amendments
	const shareholderQuotas = new Map<
		string,
		{
			info: ShareholderInfo;
			quota: number;
			lastUpdated: string;
		}
	>();

	// Process entries chronologically to build current state
	const sortedEntries = [
		...businessRegistrationReport.inscriptionsAndEndorsements,
	].sort(
		(a, b) =>
			new Date(a.date || "1900-01-01").getTime() -
			new Date(b.date || "1900-01-01").getTime(),
	);

	for (const entry of sortedEntries) {
		if (entry.subject === "incorporation" && "shareholders" in entry) {
			// Initialize with incorporation shareholders
			entry.shareholders.forEach((shareholder) => {
				const canonicalName = getCanonicalName(shareholder.name.full, nameMap);
				shareholderQuotas.set(canonicalName, {
					info: {
						...shareholder,
						name: {
							...shareholder.name,
							full: canonicalName, // Use canonical name
						},
					},
					quota: shareholder.quota.amount,
					lastUpdated: entry.date || "",
				});
			});
		}

		if (
			entry.subject === "quota_transfer" &&
			"activeParties" in entry &&
			"passiveParties" in entry
		) {
			// Handle quota transfers with name mapping
			entry.activeParties.forEach((activeParty) => {
				const canonicalName = getCanonicalName(activeParty.name.full, nameMap);
				shareholderQuotas.set(canonicalName, {
					info: {
						...activeParty,
						name: {
							...activeParty.name,
							full: canonicalName,
						},
					},
					quota: activeParty.quota.amount,
					lastUpdated: entry.date || "",
				});
			});

			// Remove or reduce passive parties' quotas with name mapping
			entry.passiveParties.forEach((passiveParty) => {
				const canonicalName = getCanonicalName(
					passiveParty.shareholder.full,
					nameMap,
				);
				const existingShareholder = shareholderQuotas.get(canonicalName);
				if (existingShareholder) {
					if (passiveParty.quota) {
						// Reduce quota by transferred amount
						existingShareholder.quota -= passiveParty.quota.amount;
						if (existingShareholder.quota <= 0) {
							shareholderQuotas.delete(canonicalName);
						}
					} else {
						// Complete transfer - remove shareholder
						shareholderQuotas.delete(canonicalName);
					}
				}
			});
		}

		if (
			entry.subject === "bylaws_amendment" &&
			"alterations" in entry &&
			entry.alterations.shareholders
		) {
			// Handle bylaws amendments that change shareholders with name mapping
			entry.alterations.shareholders.forEach((shareholder) => {
				const canonicalName = getCanonicalName(shareholder.name.full, nameMap);
				shareholderQuotas.set(canonicalName, {
					info: {
						...shareholder,
						name: {
							...shareholder.name,
							full: canonicalName,
						},
					},
					quota: shareholder.quota.amount,
					lastUpdated: entry.date || "",
				});
			});
		}
	}

	return Array.from(shareholderQuotas.values()).map((sh) => sh.info);
}

export function getCurrentAdministrators() {
	// Find the most recent administration information
	const adminEntries = businessRegistrationReport.inscriptionsAndEndorsements
		.filter(
			(entry) =>
				(entry.subject === "appointment" && "administration" in entry) ||
				(entry.subject === "bylaws_amendment" &&
					"alterations" in entry &&
					entry.alterations.administration) ||
				(entry.subject === "incorporation" && "administration" in entry),
		)
		.sort(
			(a, b) =>
				new Date(b.date || "1900-01-01").getTime() -
				new Date(a.date || "1900-01-01").getTime(),
		);

	if (adminEntries.length === 0) return [];

	const latestEntry = adminEntries[0];
	let administration: {
		administrators: Array<{
			name: { full: string; chinese?: string; portuguese?: string };
			sex?: string;
			civilStatus?: string;
			domicile?: { full: string };
		}>;
		formOfObligation?: string;
		composition?: string;
	} | null = null;

	if (
		latestEntry.subject === "appointment" &&
		"administration" in latestEntry
	) {
		administration = latestEntry.administration;
	} else if (
		latestEntry.subject === "bylaws_amendment" &&
		"alterations" in latestEntry &&
		latestEntry.alterations.administration
	) {
		administration = latestEntry.alterations.administration;
	} else if (
		latestEntry.subject === "incorporation" &&
		"administration" in latestEntry
	) {
		administration = latestEntry.administration;
	}

	if (!administration) return [];

	return administration.administrators.map((admin) => ({
		name: admin.name.full,
		name_zh: admin.name.chinese,
		name_pt: admin.name.portuguese,
		title: "管理員",
		sex: admin.sex,
		civil_status: admin.civilStatus,
		domicile: admin.domicile?.full,
		signature_method: administration.formOfObligation,
		appointment_date:
			latestEntry.date ||
			businessRegistrationReport.reportInformation.informationAsOf,
		composition: administration.composition,
	}));
}

export function getAllEvents() {
	return businessRegistrationReport.inscriptionsAndEndorsements.map(
		(entry, index) => ({
			event_id: index + 1,
			date:
				entry.date ||
				extractDateFromApplicationNumber(entry.applicationNumber) ||
				businessRegistrationReport.reportInformation.informationAsOf,
			type: entry.subjectDescription.chinese,
			type_pt: entry.subjectDescription.portuguese,
			entry_type: entry.entryType,
			subject: entry.subject,
			application_number: entry.applicationNumber,
			registration_number: entry.registrationNumber,
			documents: entry.documents,
			registrar: entry.registrar,
			status: entry.status || "active",
			details: entry,

			// Enhanced information based on entry type
			...(entry.subject === "quota_transfer" && "activeParties" in entry
				? {
						quota_transfer: {
							cause: entry.cause,
							active_parties: entry.activeParties.map((p) => p.name.full),
							passive_parties: entry.passiveParties.map(
								(p) => p.shareholder.full,
							),
							value:
								typeof entry.value === "string"
									? entry.value
									: entry.value.formatted,
						},
					}
				: {}),

			...(entry.subject === "bylaws_amendment" && "alterations" in entry
				? {
						bylaws_amendment: {
							capital_change: entry.alterations.capital?.amount,
							new_shareholders: entry.alterations.shareholders?.length || 0,
							administration_change: !!entry.alterations.administration,
							form_of_obligation: entry.alterations.formOfObligation,
						},
					}
				: {}),

			...(entry.subject === "incorporation" && "companyName" in entry
				? {
						incorporation: {
							company_name: entry.companyName,
							initial_capital: entry.capital.amount,
							initial_shareholders: entry.shareholders.length,
							business_objects: entry.businessObjects,
						},
					}
				: {}),
		}),
	);
}

// Helper function to build name change mappings
export function buildNameChangeMappings(): Map<string, string> {
	const nameMap = new Map<string, string>();

	businessRegistrationReport.inscriptionsAndEndorsements.forEach((entry) => {
		if (
			entry.subject === "person_name_change" &&
			"oldName" in entry &&
			"newName" in entry
		) {
			const oldName = entry.oldName?.full;
			const newName = entry.newName?.full;
			if (oldName && newName) {
				nameMap.set(oldName, newName);
			}
		}
	});

	return nameMap;
}

// Helper function to get canonical name (latest name after all changes)
export function getCanonicalName(
	name: string,
	nameMap: Map<string, string>,
): string {
	let currentName = name;
	const visited = new Set<string>();

	// Follow the chain of name changes
	while (nameMap.has(currentName) && !visited.has(currentName)) {
		visited.add(currentName);
		const nextName = nameMap.get(currentName);
		if (nextName) {
			currentName = nextName;
		} else {
			break;
		}
	}

	return currentName;
}

// Helper function to extract date from application number
function extractDateFromApplicationNumber(
	applicationNumber: string,
): string | null {
	// Handle different application number formats

	// Format 1: "AP. 51/31082020" - ddmmyyyy at the end
	let match = applicationNumber.match(/(\d{2})(\d{2})(\d{4})$/);
	if (match) {
		const [, day, month, year] = match;
		return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
	}

	// Format 2: "Of. 20052021" - ddmmyyyy without separator
	match = applicationNumber.match(/(\d{2})(\d{2})(\d{4})$/);
	if (match) {
		const [, day, month, year] = match;
		return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
	}

	// Format 3: "BSA-318-20250728" - yyyymmdd at the end
	match = applicationNumber.match(/(\d{4})(\d{2})(\d{2})$/);
	if (match) {
		const [, year, month, day] = match;
		return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
	}

	// Format 4: Try to find any 8-digit sequence that could be a date
	match = applicationNumber.match(/(\d{8})/);
	if (match) {
		const dateStr = match[1];
		// Try ddmmyyyy format first
		if (dateStr.length === 8) {
			const day = dateStr.substring(0, 2);
			const month = dateStr.substring(2, 4);
			const year = dateStr.substring(4, 8);

			// Validate the date makes sense (day <= 31, month <= 12, year >= 2000)
			const dayNum = Number.parseInt(day, 10);
			const monthNum = Number.parseInt(month, 10);
			const yearNum = Number.parseInt(year, 10);

			if (
				dayNum <= 31 &&
				monthNum <= 12 &&
				yearNum >= 2000 &&
				yearNum <= 2030
			) {
				return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
			}

			// Try yyyymmdd format
			const yearAlt = dateStr.substring(0, 4);
			const monthAlt = dateStr.substring(4, 6);
			const dayAlt = dateStr.substring(6, 8);

			const dayAltNum = Number.parseInt(dayAlt, 10);
			const monthAltNum = Number.parseInt(monthAlt, 10);
			const yearAltNum = Number.parseInt(yearAlt, 10);

			if (
				dayAltNum <= 31 &&
				monthAltNum <= 12 &&
				yearAltNum >= 2000 &&
				yearAltNum <= 2030
			) {
				return `${yearAlt}-${monthAlt.padStart(2, "0")}-${dayAlt.padStart(2, "0")}`;
			}
		}
	}

	return null;
}

// Enhanced helper functions
function getCurrentCapital(): number {
	// Find the most recent capital amount from bylaws amendments or incorporation
	const capitalEntries = businessRegistrationReport.inscriptionsAndEndorsements
		.filter(
			(entry) =>
				(entry.subject === "bylaws_amendment" &&
					"alterations" in entry &&
					entry.alterations.capital) ||
				(entry.subject === "incorporation" && "capital" in entry),
		)
		.sort(
			(a, b) =>
				new Date(b.date || "1900-01-01").getTime() -
				new Date(a.date || "1900-01-01").getTime(),
		);

	if (capitalEntries.length === 0) return 0;

	const latestEntry = capitalEntries[0];

	if (
		latestEntry.subject === "bylaws_amendment" &&
		"alterations" in latestEntry &&
		latestEntry.alterations.capital
	) {
		return latestEntry.alterations.capital.amount;
	}

	if (latestEntry.subject === "incorporation" && "capital" in latestEntry) {
		return latestEntry.capital.amount;
	}

	return 0;
}

function getCurrentAddress(): string {
	// Check for address changes first
	const addressEntries = businessRegistrationReport.inscriptionsAndEndorsements
		.filter((entry) => entry.subject === "address_change")
		.sort(
			(a, b) =>
				new Date(b.date || "1900-01-01").getTime() -
				new Date(a.date || "1900-01-01").getTime(),
		);

	if (addressEntries.length > 0 && "newAddress" in addressEntries[0]) {
		return addressEntries[0].newAddress.full;
	}

	// Fall back to report information
	return businessRegistrationReport.reportInformation.registeredAddress.full;
}

function getOriginalAddress(): string {
	const incorporationEntry =
		businessRegistrationReport.inscriptionsAndEndorsements.find(
			(entry) => entry.subject === "incorporation",
		);

	if (incorporationEntry && "registeredAddress" in incorporationEntry) {
		return incorporationEntry.registeredAddress.full;
	}

	return businessRegistrationReport.reportInformation.registeredAddress.full;
}

function getBusinessScope(): string {
	const incorporationEntry =
		businessRegistrationReport.inscriptionsAndEndorsements.find(
			(entry) => entry.subject === "incorporation",
		);

	if (incorporationEntry && "businessObjects" in incorporationEntry) {
		return incorporationEntry.businessObjects.join("、");
	}

	return "";
}

function getBusinessObjects(): string[] {
	const incorporationEntry =
		businessRegistrationReport.inscriptionsAndEndorsements.find(
			(entry) => entry.subject === "incorporation",
		);

	if (incorporationEntry && "businessObjects" in incorporationEntry) {
		return incorporationEntry.businessObjects;
	}

	return [];
}

function getEstablishmentDate(): string {
	const incorporationEntry =
		businessRegistrationReport.inscriptionsAndEndorsements.find(
			(entry) => entry.subject === "incorporation",
		);

	return (
		incorporationEntry?.date ||
		(incorporationEntry
			? extractDateFromApplicationNumber(incorporationEntry.applicationNumber)
			: null) ||
		businessRegistrationReport.reportInformation.informationAsOf
	);
}

function getTotalShareholdersEver(): number {
	const nameMap = buildNameChangeMappings();
	const shareholderNames = new Set<string>();

	businessRegistrationReport.inscriptionsAndEndorsements.forEach((entry) => {
		if ("shareholders" in entry && entry.shareholders) {
			entry.shareholders.forEach((shareholder) => {
				const canonicalName = getCanonicalName(shareholder.name.full, nameMap);
				shareholderNames.add(canonicalName);
			});
		}
		if ("activeParties" in entry && entry.activeParties) {
			entry.activeParties.forEach((party) => {
				const canonicalName = getCanonicalName(party.name.full, nameMap);
				shareholderNames.add(canonicalName);
			});
		}
		if ("passiveParties" in entry && entry.passiveParties) {
			entry.passiveParties.forEach((party) => {
				const canonicalName = getCanonicalName(party.shareholder.full, nameMap);
				shareholderNames.add(canonicalName);
			});
		}
		if ("alterations" in entry && entry.alterations.shareholders) {
			entry.alterations.shareholders.forEach((shareholder) => {
				const canonicalName = getCanonicalName(shareholder.name.full, nameMap);
				shareholderNames.add(canonicalName);
			});
		}
	});

	return shareholderNames.size;
}

function getCapitalChanges(): number {
	return businessRegistrationReport.inscriptionsAndEndorsements.filter(
		(entry) =>
			entry.subject === "bylaws_amendment" ||
			entry.subject === "quota_transfer" ||
			entry.subject === "quota_acquisition" ||
			entry.subject === "quota_unification",
	).length;
}

function getQuotaTransferCount(): number {
	return businessRegistrationReport.inscriptionsAndEndorsements.filter(
		(entry) =>
			entry.subject === "quota_transfer" ||
			entry.subject === "quota_acquisition",
	).length;
}

function getAdministratorChangeCount(): number {
	return businessRegistrationReport.inscriptionsAndEndorsements.filter(
		(entry) =>
			entry.subject === "appointment" ||
			entry.subject === "function_termination" ||
			(entry.subject === "bylaws_amendment" &&
				"alterations" in entry &&
				entry.alterations.administration),
	).length;
}

// Helper function to consolidate shareholding history by year
function consolidateHistoryByYear(
	history: Array<{
		date: string;
		event: string;
		shareholders: Array<{ name: string; quota: number; percentage: number }>;
		totalCapital: number;
	}>,
	_nameMap: Map<string, string>,
) {
	// Build a timeline of name changes
	const nameChangeTimeline = buildNameChangeTimeline();

	// Group by year
	const yearGroups = new Map<
		string,
		Array<{
			date: string;
			event: string;
			shareholders: Array<{ name: string; quota: number; percentage: number }>;
			totalCapital: number;
		}>
	>();

	history.forEach((entry) => {
		const year = new Date(entry.date).getFullYear().toString();
		if (!yearGroups.has(year)) {
			yearGroups.set(year, []);
		}
		yearGroups.get(year)?.push(entry);
	});

	// Consolidate each year group
	const consolidated: Array<{
		date: string;
		event: string;
		shareholders: Array<{ name: string; quota: number; percentage: number }>;
		totalCapital: number;
	}> = [];

	yearGroups.forEach((entries, _year) => {
		// Use the latest entry in the year as the base
		const latestEntry = entries[entries.length - 1];
		const entryDate = new Date(latestEntry.date);

		// Consolidate shareholders using historical names (names that were current at that time)
		const shareholderMap = new Map<
			string,
			{ quota: number; percentage: number }
		>();

		// Process all shareholders from the latest state in that year
		latestEntry.shareholders.forEach(
			(sh: { name: string; quota: number; percentage: number }) => {
				// Get the name that was current at this specific date
				const historicalName = getHistoricalNameAtDate(
					sh.name,
					entryDate,
					nameChangeTimeline,
				);
				const displayName = historicalName.split(" ")[0] || historicalName; // Extract Chinese part

				if (shareholderMap.has(displayName)) {
					const existing = shareholderMap.get(displayName);
					if (existing) {
						existing.quota += sh.quota;
						existing.percentage += sh.percentage;
					}
				} else {
					shareholderMap.set(displayName, {
						quota: sh.quota,
						percentage: sh.percentage,
					});
				}
			},
		);

		// Convert back to array and normalize percentages
		const consolidatedShareholders = Array.from(shareholderMap.entries()).map(
			([name, data]) => ({
				name,
				quota: data.quota,
				percentage: data.percentage,
			}),
		);

		// Normalize percentages to 100%
		const totalPercentage = consolidatedShareholders.reduce(
			(sum, sh) => sum + sh.percentage,
			0,
		);
		if (totalPercentage > 0 && Math.abs(totalPercentage - 100) > 0.01) {
			consolidatedShareholders.forEach((sh) => {
				sh.percentage = (sh.percentage / totalPercentage) * 100;
			});
		}

		consolidated.push({
			date: latestEntry.date,
			event: latestEntry.event,
			shareholders: consolidatedShareholders,
			totalCapital: latestEntry.totalCapital,
		});
	});

	// Sort by date
	return consolidated.sort(
		(a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
	);
}

// Helper function to build a timeline of name changes
function buildNameChangeTimeline() {
	const nameChanges: Array<{
		date: Date;
		oldName: string;
		newName: string;
		personId: string; // Use full name as person identifier
	}> = [];

	businessRegistrationReport.inscriptionsAndEndorsements.forEach((entry) => {
		if (
			entry.subject === "person_name_change" &&
			"oldName" in entry &&
			"newName" in entry
		) {
			const oldName = entry.oldName?.full;
			const newName = entry.newName?.full;
			if (oldName && newName) {
				const changeDate = new Date(
					entry.date ||
						extractDateFromApplicationNumber(entry.applicationNumber) ||
						businessRegistrationReport.reportInformation.informationAsOf,
				);

				nameChanges.push({
					date: changeDate,
					oldName,
					newName,
					personId: oldName, // Use the original name as the person identifier
				});
			}
		}
	});

	// Sort by date
	return nameChanges.sort((a, b) => a.date.getTime() - b.date.getTime());
}

// Helper function to get the name that was current at a specific date
function getHistoricalNameAtDate(
	currentName: string,
	targetDate: Date,
	nameChangeTimeline: Array<{ date: Date; oldName: string; newName: string }>,
): string {
	// Find all name changes that affect this person, up to the target date
	const relevantChanges = nameChangeTimeline.filter((change) => {
		// Check if this name change involves the current name (either as old or new name)
		const isRelevant =
			change.oldName === currentName || change.newName === currentName;
		const isBeforeTargetDate = change.date <= targetDate;

		return isRelevant && isBeforeTargetDate;
	});

	if (relevantChanges.length === 0) {
		return currentName;
	}

	// Sort by date and trace the name backwards
	relevantChanges.sort((a, b) => a.date.getTime() - b.date.getTime());

	// Work backwards from the current name to find what it was at the target date
	let nameAtDate = currentName;

	// Go through changes in reverse chronological order
	for (let i = relevantChanges.length - 1; i >= 0; i--) {
		const change = relevantChanges[i];

		if (change.date > targetDate) {
			// This change happened after our target date, so we need to use the old name
			if (change.newName === nameAtDate) {
				nameAtDate = change.oldName;
			}
		}
	}

	return nameAtDate;
}

// New comprehensive shareholding history function with name change tracking
export function getShareholdingHistory() {
	const nameMap = buildNameChangeMappings();

	const history: Array<{
		date: string;
		event: string;
		shareholders: { name: string; quota: number; percentage: number }[];
		totalCapital: number;
	}> = [];

	const currentShareholders = new Map<
		string,
		{ info: ShareholderInfo; quota: number }
	>();
	let currentCapital = 0;

	const sortedEntries = [
		...businessRegistrationReport.inscriptionsAndEndorsements,
	]
		.filter(
			(entry) =>
				entry.subject === "incorporation" ||
				entry.subject === "quota_transfer" ||
				entry.subject === "bylaws_amendment",
		)
		.sort(
			(a, b) =>
				new Date(a.date || "1900-01-01").getTime() -
				new Date(b.date || "1900-01-01").getTime(),
		);

	for (const entry of sortedEntries) {
		let changed = false;

		if (
			entry.subject === "incorporation" &&
			"shareholders" in entry &&
			"capital" in entry
		) {
			currentShareholders.clear();
			entry.shareholders.forEach((sh) => {
				const canonicalName = getCanonicalName(sh.name.full, nameMap);
				currentShareholders.set(canonicalName, {
					info: {
						...sh,
						name: { ...sh.name, full: canonicalName },
					},
					quota: sh.quota.amount,
				});
			});
			currentCapital = entry.capital.amount;
			changed = true;
		}

		if (
			entry.subject === "quota_transfer" &&
			"activeParties" in entry &&
			"passiveParties" in entry
		) {
			entry.activeParties.forEach((active) => {
				const canonicalName = getCanonicalName(active.name.full, nameMap);
				currentShareholders.set(canonicalName, {
					info: {
						...active,
						name: { ...active.name, full: canonicalName },
					},
					quota: active.quota.amount,
				});
			});

			entry.passiveParties.forEach((passive) => {
				const canonicalName = getCanonicalName(
					passive.shareholder.full,
					nameMap,
				);
				const existing = currentShareholders.get(canonicalName);
				if (existing && passive.quota) {
					existing.quota -= passive.quota.amount;
					if (existing.quota <= 0) {
						currentShareholders.delete(canonicalName);
					}
				} else if (existing) {
					currentShareholders.delete(canonicalName);
				}
			});
			changed = true;
		}

		if (entry.subject === "bylaws_amendment" && "alterations" in entry) {
			if (entry.alterations.capital) {
				currentCapital = entry.alterations.capital.amount;
				changed = true;
			}
			if (entry.alterations.shareholders) {
				currentShareholders.clear();
				entry.alterations.shareholders.forEach((sh) => {
					const canonicalName = getCanonicalName(sh.name.full, nameMap);
					currentShareholders.set(canonicalName, {
						info: {
							...sh,
							name: { ...sh.name, full: canonicalName },
						},
						quota: sh.quota.amount,
					});
				});
				changed = true;
			}
		}

		if (changed) {
			const shareholders = Array.from(currentShareholders.values()).map(
				(sh) => ({
					name: sh.info.name.chinese || sh.info.name.full,
					quota: sh.quota,
					percentage:
						currentCapital > 0 ? (sh.quota / currentCapital) * 100 : 0,
				}),
			);

			// Ensure percentages add up to 100% by normalizing if needed
			const totalPercentage = shareholders.reduce(
				(sum, sh) => sum + sh.percentage,
				0,
			);
			if (totalPercentage > 0 && Math.abs(totalPercentage - 100) > 0.01) {
				shareholders.forEach((sh) => {
					sh.percentage = (sh.percentage / totalPercentage) * 100;
				});
			}

			const entryDate =
				entry.date ||
				extractDateFromApplicationNumber(entry.applicationNumber) ||
				businessRegistrationReport.reportInformation.informationAsOf;

			history.push({
				date: entryDate,
				event: entry.subjectDescription.chinese,
				shareholders,
				totalCapital: currentCapital,
			});
		}
	}

	// Consolidate history by year and use canonical names
	const consolidatedHistory = consolidateHistoryByYear(history, nameMap);

	return consolidatedHistory;
}

// New administrator history function
export function getAdministratorHistory() {
	return businessRegistrationReport.inscriptionsAndEndorsements
		.filter(
			(entry) =>
				entry.subject === "appointment" ||
				entry.subject === "function_termination" ||
				entry.subject === "incorporation" ||
				(entry.subject === "bylaws_amendment" &&
					"alterations" in entry &&
					entry.alterations.administration),
		)
		.map((entry) => {
			let administrators: string[] = [];
			let action = "";

			if (entry.subject === "appointment" && "administration" in entry) {
				administrators = entry.administration.administrators.map(
					(a) => a.name.full,
				);
				action = "任命";
			} else if (entry.subject === "function_termination") {
				if ("dismissed" in entry && entry.dismissed)
					action = `解僱: ${entry.dismissed}`;
				if ("renunciant" in entry && entry.renunciant)
					action = `辭職: ${entry.renunciant}`;
			} else if (
				entry.subject === "incorporation" &&
				"administration" in entry
			) {
				administrators = entry.administration.administrators.map(
					(a) => a.name.full,
				);
				action = "設立時任命";
			} else if (
				entry.subject === "bylaws_amendment" &&
				"alterations" in entry &&
				entry.alterations.administration
			) {
				administrators = entry.alterations.administration.administrators.map(
					(a) => a.name.full,
				);
				action = "章程修改";
			}

			return {
				date:
					entry.date ||
					extractDateFromApplicationNumber(entry.applicationNumber) ||
					businessRegistrationReport.reportInformation.informationAsOf,
				action,
				administrators,
				type: entry.subjectDescription.chinese,
				entry_type: entry.entryType,
			};
		})
		.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
}

// Comprehensive color palette with visually distinct colors
const DISTINCT_COLORS = [
	"#3B82F6", // Blue
	"#10B981", // Emerald
	"#F59E0B", // Amber
	"#EF4444", // Red
	"#8B5CF6", // Violet
	"#F97316", // Orange
	"#06B6D4", // Cyan
	"#84CC16", // Lime
	"#EC4899", // Pink
	"#6366F1", // Indigo
	"#14B8A6", // Teal
	"#F97316", // Orange (variant)
	"#DC2626", // Red (variant)
	"#7C3AED", // Purple
	"#059669", // Green
	"#0891B2", // Sky
	"#CA8A04", // Yellow
	"#BE123C", // Rose
	"#4338CA", // Blue (variant)
	"#047857", // Emerald (variant)
	"#92400E", // Brown
	"#1F2937", // Gray
	"#374151", // Gray (variant)
	"#4B5563", // Gray (lighter)
];

// Function to assign colors to all shareholders (active and historical)
function assignShareholderColors() {
	const nameMap = buildNameChangeMappings();
	const allShareholderNames = new Set<string>();

	// Collect all unique canonical names from all entries
	businessRegistrationReport.inscriptionsAndEndorsements.forEach((entry) => {
		if ("shareholders" in entry && entry.shareholders) {
			entry.shareholders.forEach((shareholder) => {
				const canonicalName = getCanonicalName(shareholder.name.full, nameMap);
				const displayName = canonicalName.split(" ")[0] || canonicalName;
				allShareholderNames.add(displayName);
			});
		}
		if ("activeParties" in entry && entry.activeParties) {
			entry.activeParties.forEach((party) => {
				const canonicalName = getCanonicalName(party.name.full, nameMap);
				const displayName = canonicalName.split(" ")[0] || canonicalName;
				allShareholderNames.add(displayName);
			});
		}
		if ("passiveParties" in entry && entry.passiveParties) {
			entry.passiveParties.forEach((party) => {
				const canonicalName = getCanonicalName(party.shareholder.full, nameMap);
				const displayName = canonicalName.split(" ")[0] || canonicalName;
				allShareholderNames.add(displayName);
			});
		}
		if ("alterations" in entry && entry.alterations.shareholders) {
			entry.alterations.shareholders.forEach((shareholder) => {
				const canonicalName = getCanonicalName(shareholder.name.full, nameMap);
				const displayName = canonicalName.split(" ")[0] || canonicalName;
				allShareholderNames.add(displayName);
			});
		}
	});

	// Sort names for consistent assignment
	const sortedNames = Array.from(allShareholderNames).sort();

	// Create color mapping
	const colorMap = new Map<string, string>();
	sortedNames.forEach((name, index) => {
		colorMap.set(name, DISTINCT_COLORS[index % DISTINCT_COLORS.length]);
	});

	return colorMap;
}

// Export the color mapping for use in components
export const shareholderColorMap = assignShareholderColors();

// Enhanced interface for modern components
export const companyData = {
	company_info: getCompanyInfo(),
	history: getAllEvents(),
	current_administrators: getCurrentAdministrators(),
	shareholders: getCurrentShareholders().map((shareholder, index) => ({
		id: String.fromCharCode(65 + index), // A, B, C, etc.
		name: shareholder.name.full,
		name_zh: shareholder.name.chinese,
		name_pt: shareholder.name.portuguese,
		gender: shareholder.sex === "M" ? "男" : "女",
		sex: shareholder.sex,
		marital_status:
			shareholder.civilStatus === "married"
				? "已婚"
				: shareholder.civilStatus === "single"
					? "單身"
					: shareholder.civilStatus === "divorced"
						? "離婚"
						: shareholder.civilStatus === "widowed"
							? "喪偶"
							: "未知",
		civil_status: shareholder.civilStatus,
		spouse: shareholder.spouse?.full,
		spouse_zh: shareholder.spouse?.chinese,
		spouse_pt: shareholder.spouse?.portuguese,
		regime:
			shareholder.propertyRegime === "separation"
				? "分別財產制"
				: shareholder.propertyRegime === "community"
					? "共同財產制"
					: shareholder.propertyRegime === "general_community"
						? "一般共同財產制"
						: null,
		property_regime: shareholder.propertyRegime,
		current_stake: shareholder.quota?.amount || 0,
		quota_formatted: shareholder.quota?.formatted || "0",
		percentage:
			shareholder.percentage ||
			((shareholder.quota?.amount || 0) / Math.max(getCurrentCapital(), 1)) *
				100,
		address: shareholder.domicile?.full,
		domicile: shareholder.domicile,
		stake_history: [], // Will be populated by shareholding history function
	})),

	// Enhanced data access
	shareholding_history: getShareholdingHistory(),
	administrator_history: getAdministratorHistory(),

	// Quick access to derived data
	current_shareholders_count: getCurrentShareholders().length,
	active_shareholders: getCurrentShareholders()
		.filter((sh) => (sh.quota?.amount || 0) > 0)
		.map((shareholder, index) => ({
			id: String.fromCharCode(65 + index),
			name: shareholder.name.full,
			name_zh: shareholder.name.chinese,
			name_pt: shareholder.name.portuguese,
			current_stake: shareholder.quota?.amount || 0,
			percentage:
				((shareholder.quota?.amount || 0) / Math.max(getCurrentCapital(), 1)) *
				100,
		})),

	// Report metadata
	report_metadata: {
		application_number:
			businessRegistrationReport.reportInformation.applicationNumber,
		registration_number:
			businessRegistrationReport.reportInformation.registrationNumber,
		report_title: businessRegistrationReport.reportInformation.reportTitle,
		registry: businessRegistrationReport.reportInformation.registry,
		information_as_of:
			businessRegistrationReport.reportInformation.informationAsOf,
		print_date: businessRegistrationReport.reportInformation.printDate,
	},
};
