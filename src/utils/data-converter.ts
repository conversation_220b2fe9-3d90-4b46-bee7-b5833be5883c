import type { BusinessRegistrationReport } from "../types/business-registration-types";
import type { CompanyData } from "../types/component-types";

/**
 * Converts a processed BusinessRegistrationReport to the CompanyData format
 * expected by the dashboard components
 */
export function convertToCompanyData(report: BusinessRegistrationReport): CompanyData {
	const reportInfo = report.reportInformation;
	const entries = report.inscriptionsAndEndorsements;
	
	// Find incorporation entry for initial data
	const incorporationEntry = entries.find(entry => entry.subject === "incorporation");
	
	// Get current and original addresses
	const addressChangeEntries = entries.filter(entry => entry.subject === "address_change");
	const currentAddress = addressChangeEntries.length > 0 
		? addressChangeEntries[addressChangeEntries.length - 1]?.newAddress?.full || reportInfo.registeredAddress.full
		: reportInfo.registeredAddress.full;
	const originalAddress = incorporationEntry?.registeredAddress?.full || reportInfo.registeredAddress.full;
	
	// Get current capital from the latest bylaws amendment or incorporation
	const bylawsAmendments = entries.filter(entry => entry.subject === "bylaws_amendment");
	const latestCapitalEntry = bylawsAmendments.length > 0 
		? bylawsAmendments[bylawsAmendments.length - 1] as any
		: incorporationEntry as any;
	const currentCapital = latestCapitalEntry?.capital?.amount || latestCapitalEntry?.alterations?.capital?.amount || 0;
	
	// Get current shareholders from latest bylaws amendment or incorporation
	const latestShareholdersEntry = bylawsAmendments.length > 0 
		? bylawsAmendments[bylawsAmendments.length - 1] as any
		: incorporationEntry as any;
	const currentShareholders = latestShareholdersEntry?.shareholders || latestShareholdersEntry?.alterations?.shareholders || [];
	
	// Process shareholders data
	const activeShareholdersList = currentShareholders.map((shareholder: any, index: number) => ({
		id: `shareholder-${index}`,
		name: shareholder.name.full,
		name_zh: shareholder.name.chinese || shareholder.name.full,
		name_pt: shareholder.name.portuguese || "",
		current_stake: shareholder.quota.amount,
		percentage: shareholder.percentage || 0,
		gender: shareholder.sex === "M" ? "男" : shareholder.sex === "F" ? "女" : null,
		marital_status: shareholder.civilStatus === "married" ? "已婚" : 
					  shareholder.civilStatus === "single" ? "單身" : 
					  shareholder.civilStatus === "divorced" ? "離婚" : 
					  shareholder.civilStatus === "widowed" ? "寡婦/寡夫" : null,
		spouse_zh: shareholder.spouse?.chinese || null,
		regime: shareholder.propertyRegime === "separation" ? "分別財產制" :
				shareholder.propertyRegime === "community" ? "夫妻財產共同制" :
				shareholder.propertyRegime === "general_community" ? "一般共同財產制" : null,
		property_regime: shareholder.propertyRegime || "",
		address: shareholder.domicile.full,
		currency: shareholder.quota.currency,
		is_active: true,
		has_no_data: false,
		historical_stake: shareholder.quota.amount,
	}));
	
	// Process history/timeline data
	const historyData = entries.map((entry, index) => ({
		event_id: index + 1,
		date: entry.date || "",
		type: entry.subjectDescription.chinese,
		type_pt: entry.subjectDescription.portuguese,
		entry_type: entry.entryType === "inscription" ? "登記" : "附註",
		subject: entry.subject,
		application_number: entry.applicationNumber,
		registration_number: entry.registrationNumber || "",
		documents: entry.documents,
		registrar: entry.registrar,
		status: "active",
		details: {
			...entry.details,
			// Add entry-specific data
			...(entry.subject === "quota_transfer" && {
				quota_transfer: {
					from: (entry as any).passiveParties?.[0]?.shareholder?.full || "",
					to: (entry as any).activeParties?.[0]?.name?.full || "",
					amount: (entry as any).value && typeof (entry as any).value !== 'string' ? (entry as any).value.amount : 0,
				}
			}),
			...(entry.subject === "person_name_change" && {
				name_change: {
					old_name: (entry as any).oldName?.full || "",
					new_name: (entry as any).newName?.full || "",
				}
			}),
			...(entry.subject === "civil_status_change" && {
				status_change: {
					person: (entry as any).person?.full || "",
					old_status: (entry as any).oldStatus || "",
					new_status: (entry as any).newStatus || "",
				}
			}),
		},
	}));
	
	// Count various statistics
	const totalShareholdersEver = new Set(
		entries.flatMap(entry => [
			...((entry as any).shareholders || []).map((s: any) => s.name.full),
			...((entry as any).activeParties || []).map((p: any) => p.name.full),
			...((entry as any).passiveParties || []).map((p: any) => p.shareholder.full),
			...((entry as any).alterations?.shareholders || []).map((s: any) => s.name.full),
		])
	).size;
	
	const quotaTransfers = entries.filter(entry => entry.subject === "quota_transfer").length;
	const administratorChanges = entries.filter(entry => 
		entry.subject === "appointment" || entry.subject === "function_termination"
	).length;
	
	// Count capital changes
	const capitalChanges = entries.filter(entry => 
		entry.subject === "bylaws_amendment" || entry.subject === "quota_transfer"
	).length;
	
	// Get business objects from incorporation
	const businessObjects = incorporationEntry?.businessObjects || ["未知"];
	
	// Create the CompanyData object
	const companyData: CompanyData = {
		company_info: {
			registration_number: reportInfo.registrationNumber,
			application_number: reportInfo.applicationNumber,
			current_name: {
				zh: reportInfo.companyName.chinese,
				pt: reportInfo.companyName.portuguese,
				en: reportInfo.companyName.english,
			},
			registry: {
				zh: reportInfo.registry.chinese,
				pt: reportInfo.registry.portuguese,
			},
			report_title: {
				zh: reportInfo.reportTitle.chinese,
				pt: reportInfo.reportTitle.portuguese,
			},
			current_capital: currentCapital,
			currency: "MOP",
			report_as_of_date: reportInfo.informationAsOf,
			print_date: reportInfo.printDate,
			establishment_date: incorporationEntry?.date || entries[0]?.date || "",
			current_address: currentAddress,
			original_address: originalAddress,
			business_scope: businessObjects.join(", "),
			business_objects: businessObjects,
			total_historical_events: entries.length,
			total_shareholders_ever: totalShareholdersEver,
			total_capital_changes: capitalChanges,
			total_quota_transfers: quotaTransfers,
			total_administrator_changes: administratorChanges,
		},
		current_shareholders_count: currentShareholders.length,
		active_shareholders: activeShareholdersList,
		shareholders: activeShareholdersList.map((s: any) => ({
			id: s.id,
			name: s.name,
			name_zh: s.name_zh,
			name_pt: s.name_pt,
			current_stake: s.current_stake,
			percentage: s.percentage,
			gender: s.gender,
			sex: s.gender === "男" ? "M" : s.gender === "女" ? "F" : undefined,
			marital_status: s.marital_status,
			civil_status: s.marital_status === "已婚" ? "married" : 
						  s.marital_status === "單身" ? "single" : 
						  s.marital_status === "離婚" ? "divorced" : 
						  s.marital_status === "寡婦/寡夫" ? "widowed" : undefined,
			spouse: s.spouse_zh,
			spouse_zh: s.spouse_zh,
			spouse_pt: s.name_pt,
			regime: s.regime,
			property_regime: s.property_regime,
			address: s.address,
			domicile: { full: s.address },
			quota: {
				amount: s.current_stake,
				currency: s.currency || "MOP",
				formatted: `${s.current_stake.toLocaleString()} ${s.currency || "MOP"}`
			},
			stake_history: [],
		})),
		shareholding_history: historyData.slice(0, 10).map((h: any) => ({
			date: h.date,
			event: h.type,
			shareholders: activeShareholdersList.map((s: any) => ({
				name: s.name,
				quota: s.current_stake,
				percentage: s.percentage
			})),
			totalCapital: currentCapital
		})),
		administrator_history: historyData.filter(h => 
			h.subject === "appointment" || h.subject === "function_termination"
		).slice(0, 5).map((h: any) => ({
			date: h.date,
			type: h.type,
			details: {
				action: h.subject === "appointment" ? "任命" : "終止職能",
				administrators: ["管理員"]
			}
		})),
		current_administrators: [],
		history: historyData,
		// Add metadata if available
		report_metadata: {
			application_number: reportInfo.applicationNumber,
			registration_number: reportInfo.registrationNumber,
			report_title: {
				chinese: reportInfo.reportTitle.chinese,
				portuguese: reportInfo.reportTitle.portuguese,
			},
			registry: {
				chinese: reportInfo.registry.chinese,
				portuguese: reportInfo.registry.portuguese,
			},
			information_as_of: reportInfo.informationAsOf,
			print_date: reportInfo.printDate,
		},
	};
	
	return companyData;
}

/**
 * Loads processed data from sessionStorage and converts it to CompanyData format
 * Returns null if no processed data is available
 */
export function loadProcessedData(): CompanyData | null {
	try {
		const storedData = sessionStorage.getItem("processedBusinessData");
		if (!storedData) {
			return null;
		}
		
		const parsedData: BusinessRegistrationReport = JSON.parse(storedData);
		return convertToCompanyData(parsedData);
	} catch (error) {
		console.error("Failed to load processed data:", error);
		return null;
	}
}

/**
 * Clears processed data from sessionStorage
 */
export function clearProcessedData(): void {
	sessionStorage.removeItem("processedBusinessData");
}