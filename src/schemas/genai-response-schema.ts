import { Type } from "@google/genai";

// Convert our BusinessRegistrationReport schema to Google GenerativeAI format
export const BusinessRegistrationReportSchema = {
	type: Type.OBJECT,
	properties: {
		reportInformation: {
			type: Type.OBJECT,
			properties: {
				reportTitle: {
					type: Type.OBJECT,
					properties: {
						chinese: { type: Type.STRING },
						portuguese: { type: Type.STRING },
					},
					required: ["chinese", "portuguese"],
				},
				registry: {
					type: Type.OBJECT,
					properties: {
						chinese: { type: Type.STRING },
						portuguese: { type: Type.STRING },
					},
					required: ["chinese", "portuguese"],
				},
				applicationNumber: { type: Type.STRING },
				registrationNumber: { type: Type.STRING },
				companyName: {
					type: Type.OBJECT,
					properties: {
						chinese: { type: Type.STRING },
						portuguese: { type: Type.STRING },
						english: { type: Type.STRING },
					},
					required: ["chinese", "portuguese", "english"],
				},
				registeredAddress: {
					type: Type.OBJECT,
					properties: {
						full: { type: Type.STRING },
					},
					required: ["full"],
				},
				informationAsOf: { type: Type.STRING },
				printDate: { type: Type.STRING },
			},
			required: [
				"reportTitle",
				"registry",
				"applicationNumber",
				"registrationNumber",
				"companyName",
				"registeredAddress",
				"informationAsOf",
				"printDate",
			],
		},
		inscriptionsAndEndorsements: {
			type: Type.ARRAY,
			items: {
				type: Type.OBJECT,
				properties: {
					entryType: {
						type: Type.STRING,
						enum: ["inscription", "endorsement"],
					},
					registrationNumber: { type: Type.STRING },
					applicationNumber: { type: Type.STRING },
					subject: {
						type: Type.STRING,
						enum: [
							"incorporation",
							"address_change",
							"function_termination",
							"person_name_change",
							"company_name_change",
							"civil_status_change",
							"quota_acquisition",
							"quota_transfer",
							"quota_unification",
							"quota_division",
							"appointment",
							"cancellation",
							"bylaws_amendment",
						],
					},
					subjectDescription: {
						type: Type.OBJECT,
						properties: {
							chinese: { type: Type.STRING },
							portuguese: { type: Type.STRING },
						},
						required: ["chinese", "portuguese"],
					},
					documents: {
						type: Type.ARRAY,
						items: { type: Type.STRING },
					},
					registrar: { type: Type.STRING },
					date: { type: Type.STRING },
					status: {
						type: Type.STRING,
						enum: ["active", "cancelled"],
					},
					details: {
						type: Type.OBJECT,
						properties: {
							description: { type: Type.STRING },
						},
						additionalProperties: true,
					},
					// Incorporation specific fields
					companyName: {
						type: Type.OBJECT,
						properties: {
							chinese: { type: Type.STRING },
							portuguese: { type: Type.STRING },
							english: { type: Type.STRING },
						},
					},
					registeredAddress: {
						type: Type.OBJECT,
						properties: {
							full: { type: Type.STRING },
						},
					},
					businessObjects: {
						type: Type.ARRAY,
						items: { type: Type.STRING },
					},
					capital: {
						type: Type.OBJECT,
						properties: {
							amount: { type: Type.NUMBER },
							currency: {
								type: Type.STRING,
								enum: ["MOP", "HKD", "USD", "CNY"],
							},
							formatted: { type: Type.STRING },
						},
					},
					shareholders: {
						type: Type.ARRAY,
						items: {
							type: Type.OBJECT,
							properties: {
								name: {
									type: Type.OBJECT,
									properties: {
										chinese: { type: Type.STRING },
										portuguese: { type: Type.STRING },
										full: { type: Type.STRING },
									},
									required: ["full"],
								},
								sex: {
									type: Type.STRING,
									enum: ["M", "F"],
								},
								civilStatus: {
									type: Type.STRING,
									enum: ["single", "married", "divorced", "widowed"],
								},
								spouse: {
									type: Type.OBJECT,
									properties: {
										chinese: { type: Type.STRING },
										portuguese: { type: Type.STRING },
										full: { type: Type.STRING },
									},
								},
								propertyRegime: {
									type: Type.STRING,
									enum: ["separation", "community", "general_community"],
								},
								domicile: {
									type: Type.OBJECT,
									properties: {
										full: { type: Type.STRING },
									},
									required: ["full"],
								},
								quota: {
									type: Type.OBJECT,
									properties: {
										amount: { type: Type.NUMBER },
										currency: {
											type: Type.STRING,
											enum: ["MOP", "HKD", "USD", "CNY"],
										},
										formatted: { type: Type.STRING },
									},
									required: ["amount", "currency", "formatted"],
								},
								percentage: { type: Type.NUMBER },
							},
							required: ["name", "sex", "civilStatus", "domicile", "quota"],
						},
					},
					administration: {
						type: Type.OBJECT,
						properties: {
							composition: { type: Type.STRING },
							administrators: {
								type: Type.ARRAY,
								items: {
									type: Type.OBJECT,
									properties: {
										name: {
											type: Type.OBJECT,
											properties: {
												chinese: { type: Type.STRING },
												portuguese: { type: Type.STRING },
												full: { type: Type.STRING },
											},
											required: ["full"],
										},
										sex: {
											type: Type.STRING,
											enum: ["M", "F"],
										},
										civilStatus: { type: Type.STRING },
										domicile: {
											type: Type.OBJECT,
											properties: {
												full: { type: Type.STRING },
											},
										},
									},
									required: ["name"],
								},
							},
							formOfObligation: { type: Type.STRING },
						},
						required: ["administrators", "formOfObligation"],
					},
					// Other entry type specific fields
					newAddress: {
						type: Type.OBJECT,
						properties: {
							full: { type: Type.STRING },
						},
					},
					person: {
						type: Type.OBJECT,
						properties: {
							chinese: { type: Type.STRING },
							portuguese: { type: Type.STRING },
							full: { type: Type.STRING },
						},
					},
					oldName: {
						type: Type.OBJECT,
						properties: {
							chinese: { type: Type.STRING },
							portuguese: { type: Type.STRING },
							full: { type: Type.STRING },
						},
					},
					newName: {
						type: Type.OBJECT,
						properties: {
							chinese: { type: Type.STRING },
							portuguese: { type: Type.STRING },
							full: { type: Type.STRING },
						},
					},
					newCompanyName: {
						type: Type.OBJECT,
						properties: {
							chinese: { type: Type.STRING },
							portuguese: { type: Type.STRING },
							english: { type: Type.STRING },
						},
					},
					oldStatus: { type: Type.STRING },
					newStatus: { type: Type.STRING },
					cause: { type: Type.STRING },
					activeParties: {
						type: Type.ARRAY,
						items: {
							type: Type.OBJECT,
							properties: {
								name: {
									type: Type.OBJECT,
									properties: {
										chinese: { type: Type.STRING },
										portuguese: { type: Type.STRING },
										full: { type: Type.STRING },
									},
									required: ["full"],
								},
								quota: {
									type: Type.OBJECT,
									properties: {
										amount: { type: Type.NUMBER },
										currency: {
											type: Type.STRING,
											enum: ["MOP", "HKD", "USD", "CNY"],
										},
										formatted: { type: Type.STRING },
									},
								},
							},
						},
					},
					passiveParties: {
						type: Type.ARRAY,
						items: {
							type: Type.OBJECT,
							properties: {
								shareholder: {
									type: Type.OBJECT,
									properties: {
										chinese: { type: Type.STRING },
										portuguese: { type: Type.STRING },
										full: { type: Type.STRING },
									},
									required: ["full"],
								},
								quota: {
									type: Type.OBJECT,
									properties: {
										amount: { type: Type.NUMBER },
										currency: {
											type: Type.STRING,
											enum: ["MOP", "HKD", "USD", "CNY"],
										},
										formatted: { type: Type.STRING },
									},
								},
							},
						},
					},
					value: {
						type: Type.OBJECT,
						properties: {
							amount: { type: Type.NUMBER },
							currency: {
								type: Type.STRING,
								enum: ["MOP", "HKD", "USD", "CNY"],
							},
							formatted: { type: Type.STRING },
						},
					},
					alterations: {
						type: Type.OBJECT,
						properties: {
							capital: {
								type: Type.OBJECT,
								properties: {
									amount: { type: Type.NUMBER },
									currency: {
										type: Type.STRING,
										enum: ["MOP", "HKD", "USD", "CNY"],
									},
									formatted: { type: Type.STRING },
								},
							},
							shareholders: {
								type: Type.ARRAY,
								items: {
									type: Type.OBJECT,
									properties: {
										name: {
											type: Type.OBJECT,
											properties: {
												chinese: { type: Type.STRING },
												portuguese: { type: Type.STRING },
												full: { type: Type.STRING },
											},
											required: ["full"],
										},
										sex: {
											type: Type.STRING,
											enum: ["M", "F"],
										},
										civilStatus: {
											type: Type.STRING,
											enum: ["single", "married", "divorced", "widowed"],
										},
										quota: {
											type: Type.OBJECT,
											properties: {
												amount: { type: Type.NUMBER },
												currency: {
													type: Type.STRING,
													enum: ["MOP", "HKD", "USD", "CNY"],
												},
												formatted: { type: Type.STRING },
											},
											required: ["amount", "currency", "formatted"],
										},
										percentage: { type: Type.NUMBER },
									},
									required: ["name", "sex", "civilStatus", "quota"],
								},
							},
							administration: {
								type: Type.OBJECT,
								properties: {
									composition: { type: Type.STRING },
									administrators: {
										type: Type.ARRAY,
										items: {
											type: Type.OBJECT,
											properties: {
												name: {
													type: Type.OBJECT,
													properties: {
														chinese: { type: Type.STRING },
														portuguese: { type: Type.STRING },
														full: { type: Type.STRING },
													},
													required: ["full"],
												},
												sex: {
													type: Type.STRING,
													enum: ["M", "F"],
												},
												civilStatus: { type: Type.STRING },
											},
											required: ["name"],
										},
									},
									formOfObligation: { type: Type.STRING },
								},
								required: ["administrators", "formOfObligation"],
							},
							clause: { type: Type.STRING },
						},
					},
					dismissed: { type: Type.STRING },
					renunciant: { type: Type.STRING },
				},
				required: [
					"entryType",
					"applicationNumber",
					"subject",
					"subjectDescription",
					"documents",
					"registrar",
				],
			},
		},
		metadata: {
			type: Type.OBJECT,
			properties: {
				totalPages: { type: Type.NUMBER },
				documentId: { type: Type.STRING },
				platformId: { type: Type.STRING },
				fees: {
					type: Type.OBJECT,
					properties: {
						amount: { type: Type.NUMBER },
						currency: {
							type: Type.STRING,
							enum: ["MOP", "HKD", "USD", "CNY"],
						},
						formatted: { type: Type.STRING },
					},
				},
			},
		},
	},
	required: ["reportInformation", "inscriptionsAndEndorsements"],
};
