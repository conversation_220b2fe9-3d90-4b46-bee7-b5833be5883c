import { z } from "zod";

// Core schema types
export const MultilingualNameSchema = z.object({
	chinese: z.string().describe("Company name in Chinese characters"),
	portuguese: z.string().describe("Company name in Portuguese"),
	english: z.string().describe("Company name in English"),
});

export const MonetaryAmountSchema = z.object({
	amount: z.number().describe("Numerical value of the monetary amount (e.g., 100000 for MOP 100,000)"),
	currency: z.enum(["MOP", "HKD", "USD", "CNY"]).describe("Currency code - MOP (Macau Pataca), HKD (Hong Kong Dollar), USD (US Dollar), CNY (Chinese Yuan)"),
	formatted: z.string().describe("Human-readable formatted amount with currency symbol (e.g., 'MOP 100,000.00')"),
});

export const AddressSchema = z.object({
	full: z.string().describe("Complete address including building name, street, district, and any relevant location details in Macau"),
});

export const PersonNameSchema = z.object({
	chinese: z.string().optional().describe("Person's name in Chinese characters"),
	portuguese: z.string().optional().describe("Person's name in Portuguese"),
	full: z.string().describe("Complete name, typically combining Chinese and Portuguese names or the primary name used"),
});

// Person information schemas
export const PersonInfoSchema = z.object({
	name: PersonNameSchema.describe("Person's full name in multiple languages"),
	sex: z.enum(["M", "F"]).describe("Gender - M for Male, F for Female"),
	civilStatus: z.enum(["single", "married", "divorced", "widowed"]).describe("Marital status of the person"),
	spouse: PersonNameSchema.optional().describe("Spouse's name if the person is married"),
	propertyRegime: z
		.enum(["separation", "community", "general_community"])
		.or(z.literal(""))
		.optional()
		.describe("Property regime for married couples - separation (separate property), community (community property), general_community (general community property)"),
	domicile: AddressSchema.describe("Person's residential address"),
});

export const ShareholderInfoSchema = PersonInfoSchema.extend({
	quota: MonetaryAmountSchema.describe("Shareholder's ownership quota/share amount in the company"),
	percentage: z.number().optional().describe("Percentage of ownership (0-100, e.g., 25.5 for 25.5%)"),
	// Additional fields for UI components
	name_zh: z.string().optional().describe("Shareholder's name in Chinese (alternative field for UI)"),
	name_pt: z.string().optional().describe("Shareholder's name in Portuguese (alternative field for UI)"),
	current_stake: z.number().nullable().optional().describe("Current ownership percentage as decimal (0-1, e.g., 0.255 for 25.5%)"),
	gender: z.string().nullable().optional().describe("Gender as string (alternative to sex field)"),
	marital_status: z.string().nullable().optional().describe("Marital status as string (alternative to civilStatus field)"),
	spouse_zh: z.string().nullable().optional().describe("Spouse's name in Chinese"),
	regime: z.string().nullable().optional().describe("Property regime as string (alternative to propertyRegime field)"),
	property_regime: z.string().optional().describe("Property regime description"),
	address: z.string().nullable().optional().describe("Address as string (alternative to domicile field)"),
	currency: z.string().optional().describe("Currency code for the quota"),
	is_active: z.boolean().nullable().optional().describe("Whether the shareholder is currently active"),
	has_no_data: z.boolean().optional().describe("Flag indicating if shareholder data is missing or incomplete"),
	historical_stake: z.number().optional().describe("Previous ownership percentage for historical tracking"),
});

export const AdministratorInfoSchema = z.object({
	name: PersonNameSchema.describe("Administrator's full name in multiple languages"),
	sex: z.enum(["M", "F"]).optional().describe("Administrator's gender - M for Male, F for Female"),
	civilStatus: z.string().optional().describe("Administrator's marital status"),
	domicile: AddressSchema.optional().describe("Administrator's residential address"),
});

export const AdministrationSchema = z.object({
	composition: z.string().optional().describe("Description of the administrative structure (e.g., 'Board of Directors', 'Single Administrator')"),
	administrators: z.array(AdministratorInfoSchema).describe("List of company administrators/directors"),
	formOfObligation: z.string().describe("How the company is legally bound (e.g., 'jointly', 'severally', 'individually')"),
});

// Entry type schemas
export const EntryTypeSchema = z.enum(["inscription", "endorsement"]).describe("Type of registry entry - inscription (original registration) or endorsement (modification/annotation)");

export const EntrySubjectSchema = z.enum([
	"incorporation",
	"address_change",
	"function_termination",
	"person_name_change",
	"company_name_change",
	"civil_status_change",
	"quota_acquisition",
	"quota_transfer",
	"quota_unification",
	"quota_division",
	"appointment",
	"cancellation",
	"bylaws_amendment",
]).describe("Subject/purpose of the registry entry - what type of business change or event is being recorded");

export const BaseRegistrationEntrySchema = z.object({
	entryType: EntryTypeSchema.describe("Whether this is an inscription or endorsement"),
	registrationNumber: z.string().optional().describe("Official registration number assigned to this entry"),
	applicationNumber: z.string().describe("Application number for this registry entry"),
	subject: EntrySubjectSchema.describe("The type of business event being registered"),
	subjectDescription: z.object({
		chinese: z.string().describe("Description of the entry subject in Chinese"),
		portuguese: z.string().describe("Description of the entry subject in Portuguese"),
	}).describe("Multilingual description of what this entry is about"),
	documents: z.array(z.string()).describe("List of supporting documents referenced in this entry"),
	registrar: z.string().describe("Name of the registrar who processed this entry"),
	date: z.string().optional().describe("Date when this entry was registered (ISO format or Portuguese date format)"),
	status: z.enum(["active", "cancelled"]).optional().describe("Current status of this registry entry"),
	// Additional fields for processed data
	event_id: z.number().optional().describe("Internal event ID for tracking"),
	type: z.string().optional().describe("Entry type as string (alternative field)"),
	type_pt: z.string().optional().describe("Entry type in Portuguese"),
	entry_type: z.string().optional().describe("Entry type description"),
	details: z.record(z.string(), z.unknown()).optional().describe("Additional details specific to this entry type"),
});

// Specific entry schemas
export const IncorporationEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("incorporation").describe("This entry records the initial incorporation/formation of the company"),
	companyName: MultilingualNameSchema.describe("Official company name in multiple languages"),
	registeredAddress: AddressSchema.describe("Official registered address of the company"),
	businessObjects: z.array(z.string()).describe("List of business activities/objects the company is authorized to conduct"),
	capital: MonetaryAmountSchema.describe("Initial registered capital of the company"),
	shareholders: z.array(ShareholderInfoSchema).describe("List of initial shareholders and their ownership details"),
	administration: AdministrationSchema.describe("Company administration structure and administrators"),
	clause: z.string().optional().describe("Additional clauses or special conditions in the company's articles"),
});

export const QuotaTransferEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.enum(["quota_acquisition", "quota_transfer"]).describe("Type of quota transaction - acquisition (buying shares) or transfer (selling/transferring shares)"),
	cause: z.string().describe("Legal reason for the quota transfer (e.g., 'sale', 'inheritance', 'gift')"),
	activeParties: z.array(ShareholderInfoSchema).describe("Shareholders who are acquiring/receiving the quotas"),
	passiveParties: z.array(
		z.object({
			shareholder: PersonNameSchema.describe("Shareholder who is transferring/selling their quota"),
			quota: MonetaryAmountSchema.optional().describe("Amount of quota being transferred by this shareholder"),
		}),
	).describe("Shareholders who are transferring/selling their quotas"),
	value: z.union([MonetaryAmountSchema, z.string()]).describe("Total value of the quota transfer transaction"),
	dividedQuota: MonetaryAmountSchema.optional().describe("Quota amount that was divided as part of this transaction"),
	reservedQuota: MonetaryAmountSchema.optional().describe("Quota amount that was reserved during this transaction"),
	cededQuotas: MonetaryAmountSchema.optional().describe("Total amount of quotas that were ceded/transferred"),
});

export const QuotaUnificationEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("quota_unification").describe("This entry records the unification of multiple quotas into a single quota"),
	titleHolder: ShareholderInfoSchema.describe("Shareholder who now holds the unified quota"),
	unifiedQuota: MonetaryAmountSchema.describe("The resulting unified quota amount"),
	originalQuotas: z.array(MonetaryAmountSchema).optional().describe("List of original quota amounts that were unified"),
	unificationDetails: z.string().optional().describe("Additional details about the unification process"),
});

export const AddressChangeEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("address_change").describe("This entry records a change in the company's registered address"),
	newAddress: AddressSchema.describe("The new registered address of the company"),
});

export const PersonNameChangeEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("person_name_change").describe("This entry records a change in a person's name (shareholder, administrator, etc.)"),
	details: z.record(z.string(), z.unknown()).optional().describe("Additional details about the name change"),
	person: PersonNameSchema.describe("The person whose name is being changed"),
	oldName: PersonNameSchema.optional().describe("The person's previous name"),
	newName: PersonNameSchema.optional().describe("The person's new name"),
});

export const CompanyNameChangeEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("company_name_change").describe("This entry records a change in the company's official name"),
	details: z.record(z.string(), z.unknown()).optional().describe("Additional details about the company name change"),
	newCompanyName: MultilingualNameSchema.describe("The new company name in multiple languages"),
});

export const CivilStatusChangeEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("civil_status_change").describe("This entry records a change in a person's marital status"),
	details: z.record(z.string(), z.unknown()).optional().describe("Additional details about the civil status change"),
	person: PersonNameSchema.describe("The person whose civil status is changing"),
	oldStatus: z.string().optional().describe("The person's previous marital status"),
	newStatus: z.string().optional().describe("The person's new marital status"),
});

export const AppointmentEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("appointment").describe("This entry records the appointment of new administrators or directors"),
	administration: AdministrationSchema.describe("Details of the new administration structure and appointed administrators"),
});

export const FunctionTerminationEntrySchema =
	BaseRegistrationEntrySchema.extend({
		subject: z.literal("function_termination").describe("This entry records the termination of an administrator's or director's function"),
		dismissed: z.string().optional().describe("Name of the person who was dismissed from their function"),
		renunciant: z.string().optional().describe("Name of the person who renounced/resigned from their function"),
	});

export const BylawsAmendmentEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("bylaws_amendment").describe("This entry records amendments to the company's bylaws/articles of association"),
	alterations: z.object({
		capital: MonetaryAmountSchema.optional().describe("Changes to the company's capital structure"),
		shareholders: z.array(ShareholderInfoSchema).optional().describe("Changes to shareholder information"),
		administration: AdministrationSchema.optional().describe("Changes to the administration structure"),
		formOfObligation: z.string().optional().describe("Changes to how the company is legally bound"),
		clause: z.string().optional().describe("Changes to special clauses in the bylaws"),
	}).describe("Specific alterations made to the company's bylaws"),
});

export const CancellationEntrySchema = BaseRegistrationEntrySchema.extend({
	subject: z.literal("cancellation").describe("This entry records the cancellation of a previous registration entry"),
	cause: z.string().describe("Reason for the cancellation (e.g., 'error in registration', 'court order')"),
	annotatedInscription: z.string().optional().describe("Reference to the inscription that is being cancelled"),
	cancelled: z.string().optional().describe("Description of what was cancelled"),
});

// Union schema for all registration entries - using regular union for more flexibility
export const RegistrationEntrySchema = z.union([
	IncorporationEntrySchema,
	QuotaTransferEntrySchema,
	QuotaUnificationEntrySchema,
	AddressChangeEntrySchema,
	PersonNameChangeEntrySchema,
	CompanyNameChangeEntrySchema,
	CivilStatusChangeEntrySchema,
	AppointmentEntrySchema,
	FunctionTerminationEntrySchema,
	BylawsAmendmentEntrySchema,
	CancellationEntrySchema,
	// Fallback to base schema for unknown entry types
	BaseRegistrationEntrySchema,
]);

// Main document schema
export const BusinessRegistrationReportSchema = z.object({
	reportInformation: z.object({
		reportTitle: z.object({
			chinese: z.string().describe("Report title in Chinese"),
			portuguese: z.string().describe("Report title in Portuguese"),
		}).describe("Title of the business registration report"),
		registry: z.object({
			chinese: z.string().describe("Registry name in Chinese"),
			portuguese: z.string().describe("Registry name in Portuguese"),
		}).describe("Name of the registry office that issued this report"),
		applicationNumber: z.string().describe("Application number for this registration report"),
		registrationNumber: z.string().describe("Official company registration number"),
		companyName: MultilingualNameSchema.describe("Official company name in multiple languages"),
		registeredAddress: AddressSchema.describe("Company's official registered address"),
		informationAsOf: z.string().describe("Date as of which the information in this report is current"),
		printDate: z.string().describe("Date when this report was printed/generated"),
	}).describe("Header information about the business registration report"),
	inscriptionsAndEndorsements: z.array(RegistrationEntrySchema).describe("List of all registration entries (inscriptions and endorsements) for this company"),
	metadata: z
		.object({
			totalPages: z.number().optional().describe("Total number of pages in the original document"),
			documentId: z.string().optional().describe("Internal document identifier"),
			platformId: z.string().optional().describe("Platform-specific identifier"),
			fees: MonetaryAmountSchema.optional().describe("Fees associated with obtaining this report"),
		})
		.optional()
		.describe("Additional metadata about the document"),
});

// Utility schemas
export const ParsedDateSchema = z.object({
	original: z.string().describe("Original date string as it appeared in the source document"),
	parsed: z.date().nullable().describe("Parsed date object, null if parsing failed"),
	format: z.string().describe("Detected or expected date format (e.g., 'DD/MM/YYYY', 'YYYY-MM-DD')"),
});

export const ValidationErrorSchema = z.object({
	field: z.string().describe("Name of the field that has the validation issue"),
	message: z.string().describe("Human-readable description of the validation issue"),
	severity: z.enum(["error", "warning", "info"]).describe("Severity level - error (blocks processing), warning (potential issue), info (informational)"),
});

export const ParseResultSchema = z.object({
	data: z.unknown().describe("The parsed data object"),
	errors: z.array(ValidationErrorSchema).describe("List of validation errors that occurred during parsing"),
	warnings: z.array(ValidationErrorSchema).describe("List of validation warnings that occurred during parsing"),
});

// Export types inferred from schemas
export type BusinessRegistrationReportZod = z.infer<
	typeof BusinessRegistrationReportSchema
>;
export type RegistrationEntryZod = z.infer<typeof RegistrationEntrySchema>;
export type ShareholderInfoZod = z.infer<typeof ShareholderInfoSchema>;
export type MonetaryAmountZod = z.infer<typeof MonetaryAmountSchema>;
