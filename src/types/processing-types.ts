import type { BusinessRegistrationReport } from "./business-registration-types";

// Processing status types
export type ProcessingStatus =
	| "idle"
	| "uploading"
	| "processing"
	| "parsing"
	| "validating"
	| "caching"
	| "completed"
	| "error";

export interface ProcessingState {
	status: ProcessingStatus;
	progress: number; // 0-100
	currentStep: string;
	error?: string;
	result?: BusinessRegistrationReport;
}

// PDF processing types
export interface PdfProcessingOptions {
	apiKey: string;
	maxRetries?: number;
	timeout?: number;
	cacheEnabled?: boolean;
}

export interface PdfProcessingResult {
	success: boolean;
	data?: BusinessRegistrationReport;
	error?: string;
	cached?: boolean;
	processingTime?: number;
}

// Cache types
export interface CacheEntry {
	key: string;
	data: BusinessRegistrationReport;
	timestamp: number;
	fileHash: string;
	fileName: string;
	fileSize: number;
	ttl?: number; // Time to live in milliseconds
}

export interface CacheMetadata {
	totalSize: number;
	entryCount: number;
	lastCleanup: number;
	maxSize: number; // Maximum cache size in bytes
}

// API key types
export interface ApiKeyConfig {
	geminiApiKey?: string;
	encrypted: boolean;
	lastUpdated: number;
	isValid?: boolean;
}

// Error types
export type PdfProcessingError =
	| "invalid_api_key"
	| "invalid_pdf"
	| "network_error"
	| "parsing_error"
	| "validation_error"
	| "cache_error"
	| "quota_exceeded"
	| "timeout_error"
	| "unknown_error";

export interface ProcessingError {
	type: PdfProcessingError;
	message: string;
	details?: string;
	retryable: boolean;
	timestamp: number;
}

// File types
export interface FileValidationResult {
	valid: boolean;
	error?: string;
	fileSize: number;
	fileType: string;
	fileName: string;
}

// Settings types
export interface AppSettings {
	apiKey: ApiKeyConfig;
	cache: {
		enabled: boolean;
		maxSize: number; // in MB
		ttl: number; // in hours
		autoCleanup: boolean;
	};
	processing: {
		timeout: number; // in seconds
		maxRetries: number;
		parallelProcessing: boolean;
	};
}
