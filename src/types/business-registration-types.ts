// Core Types
export interface MultilingualName {
	chinese: string;
	portuguese: string;
	english: string;
}

export interface MonetaryAmount {
	amount: number;
	currency: "MOP" | "HKD" | "USD" | "CNY";
	formatted: string; // e.g., "MOP $100,000.00"
}

export interface Address {
	full: string;
}

export interface PersonName {
	chinese?: string;
	portuguese?: string;
	full: string; // Combined name as fallback
}

// Person Information
export interface PersonInfo {
	name: PersonName;
	sex: "M" | "F";
	civilStatus: "single" | "married" | "divorced" | "widowed";
	spouse?: PersonName;
	propertyRegime?: "separation" | "community" | "general_community" | "";
	domicile: Address;
}

// Company Structure
export interface ShareholderInfo extends PersonInfo {
	quota: MonetaryAmount;
	percentage?: number; // Calculated percentage of total capital
	// Additional fields for UI components
	name_zh?: string;
	name_pt?: string;
	current_stake?: number | null;
	gender?: string | null;
	marital_status?: string | null;
	spouse_zh?: string | null;
	regime?: string | null;
	property_regime?: string;
	address?: string | null;
	currency?: string;
	is_active?: boolean | null;
	has_no_data?: boolean;
	historical_stake?: number;
}

export interface AdministratorInfo {
	name: PersonName;
	sex?: "M" | "F";
	civilStatus?: string;
	domicile?: Address;
}

export interface Administration {
	composition?: string;
	administrators: AdministratorInfo[];
	formOfObligation: string; // How decisions are made
}

// Registration Entry Types
export type EntryType = "inscription" | "endorsement";
export type EntrySubject =
	| "incorporation"
	| "address_change"
	| "function_termination"
	| "person_name_change"
	| "company_name_change"
	| "civil_status_change"
	| "quota_acquisition"
	| "quota_transfer"
	| "quota_unification"
	| "quota_division"
	| "appointment"
	| "cancellation"
	| "bylaws_amendment";

export interface BaseRegistrationEntry {
	entryType: EntryType;
	registrationNumber?: string;
	applicationNumber: string;
	subject: EntrySubject;
	subjectDescription: {
		chinese: string;
		portuguese: string;
	};
	documents: string[];
	registrar: string;
	date?: string;
	status?: "active" | "cancelled";
	// Additional fields for processed data
	event_id?: number;
	type?: string;
	type_pt?: string;
	entry_type?: string;
	details?: Record<string, unknown>;
}

export interface IncorporationEntry extends BaseRegistrationEntry {
	subject: "incorporation";
	companyName: MultilingualName;
	registeredAddress: Address;
	businessObjects: string[];
	capital: MonetaryAmount;
	shareholders: ShareholderInfo[];
	administration: Administration;
	clause?: string;
}

export interface QuotaTransferEntry extends BaseRegistrationEntry {
	subject: "quota_acquisition" | "quota_transfer";
	cause: string;
	activeParties: ShareholderInfo[];
	passiveParties: {
		shareholder: PersonName;
		quota?: MonetaryAmount;
	}[];
	value: MonetaryAmount | string;
	dividedQuota?: MonetaryAmount;
	reservedQuota?: MonetaryAmount;
	cededQuotas?: MonetaryAmount;
}

export interface QuotaUnificationEntry extends BaseRegistrationEntry {
	subject: "quota_unification";
	titleHolder: ShareholderInfo;
	unifiedQuota: MonetaryAmount;
	originalQuotas?: MonetaryAmount[];
	unificationDetails?: string;
}

export interface AddressChangeEntry extends BaseRegistrationEntry {
	subject: "address_change";
	newAddress: Address;
}

export interface PersonNameChangeEntry extends BaseRegistrationEntry {
	subject: "person_name_change";
	details?: Record<string, unknown>;
	person: PersonName;
	oldName?: PersonName;
	newName?: PersonName;
}

export interface CompanyNameChangeEntry extends BaseRegistrationEntry {
	subject: "company_name_change";
	details?: Record<string, unknown>;
	newCompanyName: MultilingualName;
}

export interface CivilStatusChangeEntry extends BaseRegistrationEntry {
	subject: "civil_status_change";
	details?: Record<string, unknown>;
	person: PersonName;
	oldStatus?: string;
	newStatus?: string;
}

export interface AppointmentEntry extends BaseRegistrationEntry {
	subject: "appointment";
	administration: Administration;
}

export interface FunctionTerminationEntry extends BaseRegistrationEntry {
	subject: "function_termination";
	dismissed?: string;
	renunciant?: string;
}

export interface BylawsAmendmentEntry extends BaseRegistrationEntry {
	subject: "bylaws_amendment";
	alterations: {
		capital?: MonetaryAmount;
		shareholders?: ShareholderInfo[];
		administration?: Administration;
		formOfObligation?: string;
		clause?: string;
	};
}

export interface CancellationEntry extends BaseRegistrationEntry {
	subject: "cancellation";
	cause: string;
	annotatedInscription?: string;
	cancelled?: string;
}

export type RegistrationEntry =
	| IncorporationEntry
	| QuotaTransferEntry
	| QuotaUnificationEntry
	| AddressChangeEntry
	| PersonNameChangeEntry
	| CompanyNameChangeEntry
	| CivilStatusChangeEntry
	| AppointmentEntry
	| FunctionTerminationEntry
	| BylawsAmendmentEntry
	| CancellationEntry;

// Main Document Structure
export interface BusinessRegistrationReport {
	reportInformation: {
		reportTitle: {
			chinese: string;
			portuguese: string;
		};
		registry: {
			chinese: string;
			portuguese: string;
		};
		applicationNumber: string;
		registrationNumber: string;
		companyName: MultilingualName;
		registeredAddress: Address;
		informationAsOf: string; // ISO date string
		printDate: string; // ISO date string
	};
	inscriptionsAndEndorsements: RegistrationEntry[];
	metadata?: {
		totalPages?: number;
		documentId?: string;
		platformId?: string;
		fees?: MonetaryAmount;
	};
}

// Utility types for parsing
export interface ParsedDate {
	original: string;
	parsed: Date | null;
	format: string;
}

export interface ValidationError {
	field: string;
	message: string;
	severity: "error" | "warning" | "info";
}

export interface ParseResult<T> {
	data: T;
	errors: ValidationError[];
	warnings: ValidationError[];
}
