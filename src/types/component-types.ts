import type {
	PersonName,
	ShareholderInfo,
} from "./business-registration-types";

// Extended interfaces for better type safety
export interface ExtendedShareholderInfo extends ShareholderInfo {
	id?: string;
	property_regime?: string;
	civil_status?: string;
}

export interface ActiveParty {
	name: PersonName;
	quota: {
		amount: number;
		currency: string;
		formatted: string;
	};
}

export interface PassiveParty {
	shareholder: PersonName;
	quota?: {
		amount: number;
		currency: string;
		formatted: string;
	};
}

export interface EventDetails {
	activeParties?: ActiveParty[];
	passiveParties?: PassiveParty[];
	alterations?: {
		capital?: {
			amount: number;
			currency: string;
			formatted: string;
		};
	};
	action?: string;
	administrators?: string[];
	[key: string]: unknown;
}

// Company data interface for components
export interface CompanyData {
	company_info: {
		registration_number: string;
		application_number: string;
		current_name: {
			zh: string;
			pt: string;
			en: string;
		};
		registry: {
			zh: string;
			pt: string;
		};
		report_title: {
			zh: string;
			pt: string;
		};
		current_capital: number;
		currency: string;
		report_as_of_date: string;
		print_date: string;
		establishment_date: string;
		current_address: string;
		original_address: string;
		business_scope: string;
		business_objects: string[];
		total_historical_events: number;
		total_shareholders_ever: number;
		total_capital_changes: number;
		total_quota_transfers: number;
		total_administrator_changes: number;
	};
	history: Array<{
		event_id: number;
		date: string;
		type: string;
		type_pt: string;
		entry_type: string;
		subject: string;
		application_number: string;
		registration_number?: string;
		documents: string[];
		registrar: string;
		status: string;
		details: EventDetails | Record<string, unknown>;
	}>;
	current_administrators: Array<{
		name: string;
		name_zh?: string;
		name_pt?: string;
		title: string;
		sex?: string;
		civil_status?: string;
		domicile?: string;
		signature_method?: string;
		appointment_date?: string;
		composition?: string;
	}>;
	shareholders: ShareholderInfo[];
	shareholding_history: Array<{
		date: string;
		event: string;
		shareholders: Array<{ name: string; quota: number; percentage: number }>;
		totalCapital: number;
	}>;
	administrator_history: Array<{
		date: string;
		type: string;
		details: {
			action?: string;
			administrators?: string[];
			[key: string]: unknown;
		};
	}>;
	current_shareholders_count: number;
	active_shareholders: Array<{
		id: string;
		name: string;
		name_zh?: string;
		name_pt?: string;
		current_stake: number;
		percentage: number;
		spouse?: string;
		spouse_zh?: string;
		gender?: string;
		marital_status?: string;
		regime?: string;
	}>;
	report_metadata: {
		application_number: string;
		registration_number: string;
		report_title: {
			chinese: string;
			portuguese: string;
		};
		registry: {
			chinese: string;
			portuguese: string;
		};
		information_as_of: string;
		print_date: string;
	};
}

// Component prop types
export interface CompanyInfoSectionProps {
	data: CompanyData;
	showTimeline?: boolean;
}

export interface DocumentsSectionProps {
	data: CompanyData;
}

export interface ShareholdingSectionProps {
	data: CompanyData;
	expanded?: boolean;
}

export interface SummaryCardsSectionProps {
	data: CompanyData;
}

export interface HeaderProps {
	data: CompanyData;
}

// Document types for DocumentsSection
export interface DocumentItem {
	name: string;
	date: string;
	event_type: string;
	event_id: number;
	application_number?: string;
}

export interface DocumentGroup {
	[docType: string]: DocumentItem[];
}

// Chart data types for ShareholdingSection
export interface PieChartData {
	name: string;
	value: number;
	percentage: number;
}

export interface LineChartDataPoint {
	date: string;
	[shareholderName: string]: string | number;
}

// Utility types for component state
export interface HistoryToggleState {
	[key: string]: boolean;
}

// Latest company info type
export interface LatestCompanyInfo {
	address: string;
	businessScope: string;
	capital: number;
}
