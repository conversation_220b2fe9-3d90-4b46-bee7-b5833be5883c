import CryptoJS from "crypto-js";
import type { BusinessRegistrationReport } from "../types/business-registration-types";
import { openRouterClient } from "./openrouter-client";

export interface ProcessingProgress {
	stage:
		| "uploading"
		| "converting"
		| "analyzing"
		| "parsing"
		| "caching"
		| "completed"
		| "error";
	message: string;
	progress: number; // 0-100
	details?: string;
}

export interface ProcessingResult {
	success: boolean;
	data?: BusinessRegistrationReport;
	error?: string;
	fromCache?: boolean;
	processingTime?: number;
	tokenUsage?: {
		promptTokens: number;
		completionTokens: number;
		totalTokens: number;
	};
}

export type ProgressCallback = (progress: ProcessingProgress) => void;

const EXTRACTION_PROMPT = `You are an expert at extracting structured data from Macau business registration documents (商業登記証明書 / Certidão do Registo Comercial).

Extract ALL information from this PDF document into the structured format. The response will be automatically validated against a predefined schema.

KEY EXTRACTION REQUIREMENTS:
1. Extract report information: title, registry, application number, registration number, company name, address, dates
2. Extract ALL registration entries chronologically (inscriptions and endorsements)
3. For each entry: entry type, subject, description, documents, registrar, date, status
4. For incorporation entries: include company details, shareholders, administration directly on the entry
5. For quota transfers: include active/passive parties and transaction details
6. For name/status changes: include person details and change information

IMPORTANT FIELD GUIDELINES:
- Use exact enum values: sex ("M"/"F"), civilStatus ("single"/"married"/"divorced"/"widowed")
- Currency must be "MOP", "HKD", "USD", or "CNY"
- Dates in YYYY-MM-DD format
- Property regime: "separation", "community", or "general_community" (omit if unknown)
- For single people, omit spouse and propertyRegime fields entirely
- Entry subjects: "incorporation", "quota_acquisition", "quota_transfer", "address_change", "person_name_change", "company_name_change", "civil_status_change", "appointment", "function_termination", "cancellation", "bylaws_amendment"

Focus on accurate and complete data extraction. The schema will enforce the correct structure.`;

export class PdfProcessor {
	private static instance: PdfProcessor;

	private constructor() {}

	public static getInstance(): PdfProcessor {
		if (!PdfProcessor.instance) {
			PdfProcessor.instance = new PdfProcessor();
		}
		return PdfProcessor.instance;
	}

	public async processFile(
		file: File,
		onProgress?: ProgressCallback,
	): Promise<ProcessingResult> {
		const startTime = Date.now();

		try {
			// Stage 1: Upload validation
			onProgress?.({
				stage: "uploading",
				message: "Validating PDF file...",
				progress: 10,
			});

			if (file.type !== "application/pdf") {
				throw new Error("Only PDF files are supported");
			}

			if (file.size > 10 * 1024 * 1024) {
				// 10MB limit
				throw new Error("File size must be less than 10MB");
			}

			// Stage 2: Convert to base64
			onProgress?.({
				stage: "converting",
				message: "Converting PDF to base64...",
				progress: 20,
			});

			const base64 = await this.fileToBase64(file);
			const fileHash = this.generateFileHash(base64);

			// Check cache first
			const cached = await this.getCachedResult(fileHash);
			if (cached) {
				onProgress?.({
					stage: "completed",
					message: "Loaded from cache",
					progress: 100,
				});

				return {
					success: true,
					data: cached,
					fromCache: true,
					processingTime: Date.now() - startTime,
				};
			}

			// Stage 3: Analyze with Gemini
			onProgress?.({
				stage: "analyzing",
				message: "Analyzing document with AI...",
				progress: 40,
				details: "This may take 30-60 seconds for complex documents",
			});

			const response = await openRouterClient.processDocument(
				base64,
				EXTRACTION_PROMPT,
			);

			console.log("response: ", response);

			// Stage 4: Parse response
			onProgress?.({
				stage: "parsing",
				message: "Parsing extracted data...",
				progress: 80,
			});

			// Use validated parsed data if available, otherwise fall back to manual parsing
			const parsedData =
				response.parsedData || this.parseOpenRouterResponse(response.text);

			// Stage 5: Cache result
			onProgress?.({
				stage: "caching",
				message: "Saving to cache...",
				progress: 90,
			});

			await this.cacheResult(fileHash, parsedData, file.name);

			onProgress?.({
				stage: "completed",
				message: "Processing completed successfully",
				progress: 100,
			});

			return {
				success: true,
				data: parsedData,
				fromCache: false,
				processingTime: Date.now() - startTime,
				tokenUsage: response.usage,
			};
		} catch (error) {
			onProgress?.({
				stage: "error",
				message:
					error instanceof Error ? error.message : "Unknown error occurred",
				progress: 0,
			});

			return {
				success: false,
				error:
					error instanceof Error ? error.message : "Unknown error occurred",
				processingTime: Date.now() - startTime,
			};
		}
	}

	private async fileToBase64(file: File): Promise<string> {
		return new Promise((resolve, reject) => {
			const reader = new FileReader();
			reader.onload = () => {
				const result = reader.result as string;
				// Remove data URL prefix to get pure base64
				const base64 = result.split(",")[1];
				resolve(base64);
			};
			reader.onerror = () => reject(new Error("Failed to read file"));
			reader.readAsDataURL(file);
		});
	}

	private generateFileHash(base64: string): string {
		return CryptoJS.SHA256(base64).toString(CryptoJS.enc.Hex);
	}

	private parseOpenRouterResponse(
		responseText: string,
	): BusinessRegistrationReport {
		try {
			// Clean the response text
			let cleanedResponse = responseText.trim();

			// Remove markdown code blocks if present
			if (cleanedResponse.startsWith("```json")) {
				cleanedResponse = cleanedResponse
					.replace(/^```json\s*/, "")
					.replace(/\s*```$/, "");
			} else if (cleanedResponse.startsWith("```")) {
				cleanedResponse = cleanedResponse
					.replace(/^```\s*/, "")
					.replace(/\s*```$/, "");
			}

			const parsed = JSON.parse(cleanedResponse);

			// Basic validation
			if (!parsed.reportInformation || !parsed.inscriptionsAndEndorsements) {
				throw new Error("Invalid response structure: missing required fields");
			}

			if (!Array.isArray(parsed.inscriptionsAndEndorsements)) {
				throw new Error(
					"Invalid response structure: inscriptionsAndEndorsements must be an array",
				);
			}

			return parsed as BusinessRegistrationReport;
		} catch (error) {
			if (error instanceof SyntaxError) {
				throw new Error(
					`Failed to parse AI response as JSON: ${error.message}`,
				);
			}
			throw error;
		}
	}

	private async getCachedResult(
		fileHash: string,
	): Promise<BusinessRegistrationReport | null> {
		try {
			const { cacheManager } = await import("./cache-manager");
			return await cacheManager.get(fileHash);
		} catch {
			return null;
		}
	}

	private async cacheResult(
		fileHash: string,
		data: BusinessRegistrationReport,
		fileName: string,
	): Promise<void> {
		try {
			const { cacheManager } = await import("./cache-manager");
			await cacheManager.set(fileHash, data, {
				fileName,
				processedAt: new Date().toISOString(),
				size: JSON.stringify(data).length,
			});
		} catch (error) {
			console.warn("Failed to cache result:", error);
		}
	}
}

export const pdfProcessor = PdfProcessor.getInstance();
