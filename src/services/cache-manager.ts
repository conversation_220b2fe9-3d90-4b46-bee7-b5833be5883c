import CryptoJS from "crypto-js";
import type { BusinessRegistrationReport } from "../types/business-registration-types";

const CACHE_KEY_PREFIX = "br_cache_";
const CACHE_INDEX_KEY = "br_cache_index";
const ENCRYPTION_SECRET = "br-cache-encryption-key";
const DEFAULT_TTL = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
const MAX_CACHE_SIZE = 50 * 1024 * 1024; // 50MB in bytes

export interface CacheMetadata {
	fileName: string;
	processedAt: string;
	size: number;
	ttl?: number;
}

export interface CacheEntry {
	data: BusinessRegistrationReport;
	metadata: CacheMetadata;
	createdAt: number;
	expiresAt: number;
}

export interface CacheIndex {
	[fileHash: string]: {
		fileName: string;
		processedAt: string;
		size: number;
		createdAt: number;
		expiresAt: number;
	};
}

export interface CacheStats {
	totalEntries: number;
	totalSize: number;
	oldestEntry?: string;
	newestEntry?: string;
	expiredEntries: number;
}

export class CacheManager {
	private static instance: CacheManager;

	private constructor() {
		this.cleanup();
	}

	public static getInstance(): CacheManager {
		if (!CacheManager.instance) {
			CacheManager.instance = new CacheManager();
		}
		return CacheManager.instance;
	}

	public async set(
		fileHash: string,
		data: BusinessRegistrationReport,
		metadata: CacheMetadata,
		ttl: number = DEFAULT_TTL,
	): Promise<void> {
		try {
			const now = Date.now();
			const entry: CacheEntry = {
				data,
				metadata: {
					...metadata,
					ttl,
				},
				createdAt: now,
				expiresAt: now + ttl,
			};

			// Encrypt and store the entry
			const encrypted = this.encrypt(JSON.stringify(entry));
			const cacheKey = CACHE_KEY_PREFIX + fileHash;

			localStorage.setItem(cacheKey, encrypted);

			// Update index
			await this.updateIndex(fileHash, {
				fileName: metadata.fileName,
				processedAt: metadata.processedAt,
				size: metadata.size,
				createdAt: now,
				expiresAt: now + ttl,
			});

			// Cleanup if needed
			await this.enforceStorageLimit();
		} catch (error) {
			console.error("Failed to cache entry:", error);
			throw new Error("Failed to cache processed data");
		}
	}

	public async get(
		fileHash: string,
	): Promise<BusinessRegistrationReport | null> {
		try {
			const cacheKey = CACHE_KEY_PREFIX + fileHash;
			const encrypted = localStorage.getItem(cacheKey);

			if (!encrypted) {
				return null;
			}

			const decrypted = this.decrypt(encrypted);
			const entry: CacheEntry = JSON.parse(decrypted);

			// Check if expired
			if (Date.now() > entry.expiresAt) {
				await this.delete(fileHash);
				return null;
			}

			return entry.data;
		} catch (error) {
			console.error("Failed to retrieve cached entry:", error);
			// Clean up corrupted entry
			await this.delete(fileHash);
			return null;
		}
	}

	public async delete(fileHash: string): Promise<void> {
		try {
			const cacheKey = CACHE_KEY_PREFIX + fileHash;
			localStorage.removeItem(cacheKey);

			// Update index
			const index = this.getIndex();
			delete index[fileHash];
			this.saveIndex(index);
		} catch (error) {
			console.error("Failed to delete cached entry:", error);
		}
	}

	public async clear(): Promise<void> {
		try {
			const index = this.getIndex();

			// Remove all cache entries
			for (const fileHash of Object.keys(index)) {
				const cacheKey = CACHE_KEY_PREFIX + fileHash;
				localStorage.removeItem(cacheKey);
			}

			// Clear index
			localStorage.removeItem(CACHE_INDEX_KEY);
		} catch (error) {
			console.error("Failed to clear cache:", error);
		}
	}

	public async getStats(): Promise<CacheStats> {
		const index = this.getIndex();
		const now = Date.now();

		let totalSize = 0;
		let expiredEntries = 0;
		let oldestTime = Number.POSITIVE_INFINITY;
		let newestTime = 0;
		let oldestEntry: string | undefined;
		let newestEntry: string | undefined;

		for (const [, entry] of Object.entries(index)) {
			totalSize += entry.size;

			if (now > entry.expiresAt) {
				expiredEntries++;
			}

			if (entry.createdAt < oldestTime) {
				oldestTime = entry.createdAt;
				oldestEntry = entry.fileName;
			}

			if (entry.createdAt > newestTime) {
				newestTime = entry.createdAt;
				newestEntry = entry.fileName;
			}
		}

		return {
			totalEntries: Object.keys(index).length,
			totalSize,
			oldestEntry,
			newestEntry,
			expiredEntries,
		};
	}

	public async getAllEntries(): Promise<
		Array<{
			fileHash: string;
			metadata: CacheMetadata & { createdAt: number; expiresAt: number };
		}>
	> {
		const index = this.getIndex();

		return Object.entries(index).map(([fileHash, entry]) => ({
			fileHash,
			metadata: {
				fileName: entry.fileName,
				processedAt: entry.processedAt,
				size: entry.size,
				createdAt: entry.createdAt,
				expiresAt: entry.expiresAt,
			},
		}));
	}

	private cleanup(): void {
		// Run cleanup on initialization
		this.cleanupExpired();
	}

	private async cleanupExpired(): Promise<void> {
		try {
			const index = this.getIndex();
			const now = Date.now();
			const toDelete: string[] = [];

			for (const [fileHash, entry] of Object.entries(index)) {
				if (now > entry.expiresAt) {
					toDelete.push(fileHash);
				}
			}

			for (const fileHash of toDelete) {
				await this.delete(fileHash);
			}
		} catch (error) {
			console.error("Failed to cleanup expired entries:", error);
		}
	}

	private async enforceStorageLimit(): Promise<void> {
		try {
			const stats = await this.getStats();

			if (stats.totalSize > MAX_CACHE_SIZE) {
				// Remove oldest entries until under limit
				const entries = await this.getAllEntries();
				entries.sort((a, b) => a.metadata.createdAt - b.metadata.createdAt);

				let currentSize = stats.totalSize;
				for (const entry of entries) {
					if (currentSize <= MAX_CACHE_SIZE * 0.8) {
						// Remove until 80% of limit
						break;
					}

					await this.delete(entry.fileHash);
					currentSize -= entry.metadata.size;
				}
			}
		} catch (error) {
			console.error("Failed to enforce storage limit:", error);
		}
	}

	private getIndex(): CacheIndex {
		try {
			const encrypted = localStorage.getItem(CACHE_INDEX_KEY);
			if (!encrypted) {
				return {};
			}

			const decrypted = this.decrypt(encrypted);
			return JSON.parse(decrypted);
		} catch (error) {
			console.error("Failed to load cache index:", error);
			return {};
		}
	}

	private saveIndex(index: CacheIndex): void {
		try {
			const encrypted = this.encrypt(JSON.stringify(index));
			localStorage.setItem(CACHE_INDEX_KEY, encrypted);
		} catch (error) {
			console.error("Failed to save cache index:", error);
		}
	}

	private async updateIndex(
		fileHash: string,
		metadata: CacheIndex[string],
	): Promise<void> {
		const index = this.getIndex();
		index[fileHash] = metadata;
		this.saveIndex(index);
	}

	private encrypt(data: string): string {
		return CryptoJS.AES.encrypt(data, ENCRYPTION_SECRET).toString();
	}

	private decrypt(encryptedData: string): string {
		const bytes = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_SECRET);
		return bytes.toString(CryptoJS.enc.Utf8);
	}
}

export const cacheManager = CacheManager.getInstance();
