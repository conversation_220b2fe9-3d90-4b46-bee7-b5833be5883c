import CryptoJS from "crypto-js";

const STORAGE_KEY = "openrouter_api_key";
const ENCRYPTION_SECRET = "br-analysis-app-secret-key";

export interface ApiKeyStatus {
	isValid: boolean;
	isStored: boolean;
	error?: string;
}

export class ApiKeyManager {
	private static instance: ApiKeyManager;
	private apiKey: string | null = null;

	private constructor() {
		this.loadFromStorage();
	}

	public static getInstance(): ApiKeyManager {
		if (!ApiKeyManager.instance) {
			ApiKeyManager.instance = new ApiKeyManager();
		}
		return ApiKeyManager.instance;
	}

	public setApiKey(key: string): void {
		this.apiKey = key;
		this.saveToStorage(key);
	}

	public getApiKey(): string | null {
		return this.apiKey;
	}

	public hasApiKey(): boolean {
		return this.apiKey !== null && this.apiKey.length > 0;
	}

	public clearApiKey(): void {
		this.apiKey = null;
		localStorage.removeItem(STORAGE_KEY);
	}

	public async validateApiKey(key?: string): Promise<ApiKeyStatus> {
		const keyToValidate = key || this.apiKey;

		if (!keyToValidate) {
			return {
				isValid: false,
				isStored: false,
				error: "No API key provided",
			};
		}

		if (!keyToValidate.startsWith("sk-or-")) {
			return {
				isValid: false,
				isStored: this.hasApiKey(),
				error:
					"Invalid API key format. OpenRouter API keys should start with 'sk-or-'",
			};
		}

		try {
			const response = await fetch(
				"https://openrouter.ai/api/v1/credits",
				{
					method: "GET",
					headers: {
						"Authorization": `Bearer ${keyToValidate}`,
						"Content-Type": "application/json",
					},
				}
			);

			if (response.ok) {
				const data = await response.json().catch(() => null);
				console.log("OpenRouter credits:", data);
				return {
					isValid: true,
					isStored: this.hasApiKey(),
				};
			}

			const errorData = await response.json().catch(() => null);
			return {
				isValid: false,
				isStored: this.hasApiKey(),
				error:
					errorData?.error?.message ||
					`API validation failed with status ${response.status}`,
			};
		} catch (error) {
			return {
				isValid: false,
				isStored: this.hasApiKey(),
				error: `Network error: ${error instanceof Error ? error.message : "Unknown error"}`,
			};
		}
	}

	private saveToStorage(key: string): void {
		try {
			const encrypted = CryptoJS.AES.encrypt(key, ENCRYPTION_SECRET).toString();
			localStorage.setItem(STORAGE_KEY, encrypted);
		} catch (error) {
			console.error("Failed to save API key:", error);
		}
	}

	private loadFromStorage(): void {
		try {
			const encrypted = localStorage.getItem(STORAGE_KEY);
			if (encrypted) {
				const decrypted = CryptoJS.AES.decrypt(encrypted, ENCRYPTION_SECRET);
				this.apiKey = decrypted.toString(CryptoJS.enc.Utf8);
			}
		} catch (error) {
			console.error("Failed to load API key:", error);
			localStorage.removeItem(STORAGE_KEY);
		}
	}
}

export const apiKeyManager = ApiKeyManager.getInstance();
