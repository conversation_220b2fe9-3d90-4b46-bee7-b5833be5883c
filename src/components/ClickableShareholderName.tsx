import type React from "react";
import { type ReactNode, useRef } from "react";
import { useShareholderPopup } from "../contexts/ShareholderPopupContext";
import {
	buildNameChangeMappings,
	businessRegistrationReport,
	companyData,
	getCanonicalName,
} from "../data/business-registration-data";

interface ClickableShareholderNameProps {
	shareholderName: string;
	className?: string;
	children?: ReactNode;
	displayName?: string;
}

export function ClickableShareholderName({
	shareholderName,
	className = "",
	children,
	displayName,
}: ClickableShareholderNameProps) {
	const { showPopup, hidePopup } = useShareholderPopup();
	const elementRef = useRef<HTMLButtonElement>(null);
	const hoverTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

	// Find shareholder data with latest information and status
	const findShareholderData = (name: string) => {
		// First, determine if this person is currently active by checking current shareholders
		const currentShareholder = companyData.shareholders.find(
			(s) =>
				s.name === name ||
				s.name_zh === name ||
				s.name_pt === name ||
				s.name?.includes(name) ||
				s.name_zh?.includes(name) ||
				(s.name_zh && name?.includes(s.name_zh)) ||
				(typeof s.name === "string" && name?.includes(s.name)),
		);

		// Also check active shareholders list
		const activeShareholder = companyData.active_shareholders?.find(
			(s) =>
				s.name === name ||
				s.name_zh === name ||
				s.name_pt === name ||
				s.name?.includes(name) ||
				s.name_zh?.includes(name) ||
				(s.name_zh && name?.includes(s.name_zh)) ||
				(typeof s.name === "string" && name?.includes(s.name)),
		);

		// If found in current/active shareholders, return with current status
		if (currentShareholder || activeShareholder) {
			const shareholder = currentShareholder || activeShareholder;
			return {
				...shareholder,
				is_active: true,
				current_stake: shareholder?.current_stake || 0,
				percentage: shareholder?.percentage || 0,
			};
		}

		// If not found in current shareholders, search historical data
		const nameMap = buildNameChangeMappings();
		let historicalData = null;

		for (const entry of businessRegistrationReport.inscriptionsAndEndorsements) {
			// Check in shareholders property
			if ("shareholders" in entry && entry.shareholders) {
				for (const histShareholder of entry.shareholders) {
					const canonicalName = getCanonicalName(
						histShareholder.name.full,
						nameMap,
					);
					const displayName = canonicalName.split(" ")[0] || canonicalName;
					const currentDisplayName =
						histShareholder.name.chinese ||
						histShareholder.name.full.split(" ")[0];

					if (
						displayName === name ||
						currentDisplayName === name ||
						histShareholder.name.full.includes(name) ||
						name.includes(currentDisplayName) ||
						canonicalName.includes(name) ||
						histShareholder.name.chinese === name ||
						histShareholder.name.portuguese === name
					) {
						historicalData = {
							name: histShareholder.name.full,
							name_zh: histShareholder.name.chinese,
							name_pt: histShareholder.name.portuguese,
							gender: histShareholder.sex === "M" ? "男" : "女",
							sex: histShareholder.sex,
							marital_status:
								histShareholder.civilStatus === "married"
									? "已婚"
									: histShareholder.civilStatus === "single"
										? "單身"
										: histShareholder.civilStatus === "divorced"
											? "離婚"
											: histShareholder.civilStatus === "widowed"
												? "喪偶"
												: "未知",
							civil_status: histShareholder.civilStatus,
							spouse: histShareholder.spouse?.full,
							spouse_zh: histShareholder.spouse?.chinese,
							spouse_pt: histShareholder.spouse?.portuguese,
							regime:
								histShareholder.propertyRegime === "separation"
									? "分別財產制"
									: histShareholder.propertyRegime === "community"
										? "共同財產制"
										: histShareholder.propertyRegime === "general_community"
											? "一般共同財產制"
											: null,
							property_regime: histShareholder.propertyRegime,
							address: histShareholder.domicile?.full,
							domicile: histShareholder.domicile,
							is_active: false,
							current_stake: 0, // Exited shareholders have 0 current stake
							percentage: 0, // Exited shareholders have 0 current percentage
							historical_stake: histShareholder.quota?.amount || 0, // Keep historical for reference
						};
						break;
					}
				}
			}

			// Check in activeParties (quota transfers) if not found yet
			if (!historicalData && "activeParties" in entry && entry.activeParties) {
				for (const party of entry.activeParties) {
					const canonicalName = getCanonicalName(party.name.full, nameMap);
					const displayName = canonicalName.split(" ")[0] || canonicalName;
					const currentDisplayName =
						party.name.chinese || party.name.full.split(" ")[0];

					if (
						displayName === name ||
						currentDisplayName === name ||
						party.name.full.includes(name) ||
						name.includes(currentDisplayName) ||
						canonicalName.includes(name) ||
						party.name.chinese === name ||
						party.name.portuguese === name
					) {
						historicalData = {
							name: party.name.full,
							name_zh: party.name.chinese,
							name_pt: party.name.portuguese,
							gender: party.sex === "M" ? "男" : "女",
							sex: party.sex,
							marital_status:
								party.civilStatus === "married"
									? "已婚"
									: party.civilStatus === "single"
										? "單身"
										: party.civilStatus === "divorced"
											? "離婚"
											: party.civilStatus === "widowed"
												? "喪偶"
												: "未知",
							civil_status: party.civilStatus,
							spouse: party.spouse?.full,
							spouse_zh: party.spouse?.chinese,
							spouse_pt: party.spouse?.portuguese,
							regime:
								party.propertyRegime === "separation"
									? "分別財產制"
									: party.propertyRegime === "community"
										? "共同財產制"
										: party.propertyRegime === "general_community"
											? "一般共同財產制"
											: null,
							property_regime: party.propertyRegime,
							address: party.domicile?.full,
							domicile: party.domicile,
							is_active: false,
							current_stake: 0, // Exited shareholders have 0 current stake
							percentage: 0, // Exited shareholders have 0 current percentage
							historical_stake: party.quota?.amount || 0, // Keep historical for reference
						};
						break;
					}
				}
			}

			if (historicalData) break;
		}

		// If found historical data, return it
		if (historicalData) {
			return historicalData;
		}

		// If no data found at all, return a placeholder indicating no data
		return {
			name: name,
			name_zh: name,
			name_pt: null,
			gender: null,
			marital_status: null,
			spouse: null,
			spouse_zh: null,
			regime: null,
			current_stake: null,
			percentage: null,
			address: null,
			is_active: null,
			has_no_data: true, // Flag to indicate no data available
		};
	};

	const handleMouseEnter = (_event: React.MouseEvent) => {
		// Clear any existing timeout
		if (hoverTimeoutRef.current) {
			clearTimeout(hoverTimeoutRef.current);
		}

		// Add a small delay to prevent popup flickering
		hoverTimeoutRef.current = setTimeout(() => {
			const shareholderData = findShareholderData(shareholderName);

			// Always show popup, even if no data found
			showPopup(
				{
					name:
						typeof shareholderData.name === "string"
							? { full: shareholderData.name, chinese: "", portuguese: "" }
							: shareholderData.name || {
									full: "",
									chinese: "",
									portuguese: "",
								},
					sex: (shareholderData.gender === "male"
						? "M"
						: shareholderData.gender === "female"
							? "F"
							: "M") as "M" | "F",
					civilStatus: (shareholderData.marital_status === "married"
						? "married"
						: shareholderData.marital_status === "single"
							? "single"
							: "single") as "single" | "married" | "divorced" | "widowed",
					domicile: { full: shareholderData.address || "" },
					quota: {
						amount: shareholderData.current_stake || 0,
						currency: companyData.company_info.currency as
							| "MOP"
							| "HKD"
							| "USD"
							| "CNY",
						formatted: `${shareholderData.current_stake || 0} ${companyData.company_info.currency}`,
					},
					name_zh: shareholderData.name_zh || undefined,
					name_pt: shareholderData.name_pt || undefined,
					current_stake: shareholderData.current_stake,
					percentage: shareholderData.percentage || undefined,
					gender: shareholderData.gender,
					marital_status: shareholderData.marital_status,
					spouse: shareholderData.spouse
						? typeof shareholderData.spouse === "string"
							? { full: shareholderData.spouse, chinese: "", portuguese: "" }
							: shareholderData.spouse
						: undefined,
					spouse_zh: shareholderData.spouse_zh,
					regime: shareholderData.regime,
					property_regime: shareholderData.property_regime,
					address: shareholderData.address,
					currency: companyData.company_info.currency,
					is_active: shareholderData.is_active,
					has_no_data: shareholderData.has_no_data,
					historical_stake: shareholderData.historical_stake,
				},
				0,
				0,
			); // Position ignored since popup uses fixed bottom-right
		}, 300); // 300ms delay
	};

	const handleMouseLeave = () => {
		// Clear timeout if mouse leaves before popup shows
		if (hoverTimeoutRef.current) {
			clearTimeout(hoverTimeoutRef.current);
		}

		// Add a small delay before hiding to allow moving to popup
		setTimeout(() => {
			hidePopup();
		}, 100);
	};

	const handleClick = (event?: React.MouseEvent | React.KeyboardEvent) => {
		event?.preventDefault();
		event?.stopPropagation();

		const shareholderData = findShareholderData(shareholderName);

		// Always show popup, even if no data found
		showPopup(
			{
				name:
					typeof shareholderData.name === "string"
						? { full: shareholderData.name, chinese: "", portuguese: "" }
						: shareholderData.name || { full: "", chinese: "", portuguese: "" },
				sex: (shareholderData.gender === "male"
					? "M"
					: shareholderData.gender === "female"
						? "F"
						: "M") as "M" | "F",
				civilStatus: (shareholderData.marital_status === "married"
					? "married"
					: shareholderData.marital_status === "single"
						? "single"
						: "single") as "single" | "married" | "divorced" | "widowed",
				domicile: { full: shareholderData.address || "" },
				quota: {
					amount: shareholderData.current_stake || 0,
					currency: companyData.company_info.currency as
						| "MOP"
						| "HKD"
						| "USD"
						| "CNY",
					formatted: `${shareholderData.current_stake || 0} ${companyData.company_info.currency}`,
				},
				name_zh: shareholderData.name_zh || undefined,
				name_pt: shareholderData.name_pt || undefined,
				current_stake: shareholderData.current_stake,
				percentage: shareholderData.percentage || undefined,
				gender: shareholderData.gender,
				marital_status: shareholderData.marital_status,
				spouse: shareholderData.spouse
					? typeof shareholderData.spouse === "string"
						? { full: shareholderData.spouse, chinese: "", portuguese: "" }
						: shareholderData.spouse
					: undefined,
				spouse_zh: shareholderData.spouse_zh,
				regime: shareholderData.regime,
				property_regime: shareholderData.property_regime,
				address: shareholderData.address,
				currency: companyData.company_info.currency,
				is_active: shareholderData.is_active,
				has_no_data: shareholderData.has_no_data,
				historical_stake: shareholderData.historical_stake,
			},
			0,
			0,
		); // Position ignored since popup uses fixed bottom-right
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter" || e.key === " ") {
			e.preventDefault();
			handleClick(e);
		}
	};

	return (
		<button
			type="button"
			ref={elementRef}
			className={`cursor-pointer hover:text-blue-600 hover:underline transition-colors duration-200 bg-transparent border-none p-0 font-inherit text-inherit ${className}`}
			onMouseEnter={handleMouseEnter}
			onMouseLeave={handleMouseLeave}
			onClick={handleClick}
			onKeyDown={handleKeyDown}
			title="點擊查看詳細資訊"
		>
			{children || displayName || shareholderName}
		</button>
	);
}
