import {
	Building,
	Calendar,
	Clock,
	DollarSign,
	FileText,
	Globe,
	TrendingUp,
	Users,
} from "lucide-react";
//import type { ShareholderWithHistory } from '../data/business-registration-data'
import type { SummaryCardsSectionProps } from "../types/component-types";

export function SummaryCards({ data }: SummaryCardsSectionProps) {
	const activeShareholdersCount = data.current_shareholders_count;
	const totalHistoricalShareholders = data.company_info.total_shareholders_ever;

	// Check for international shareholders based on address patterns
	const hasInternationalShareholder = data.shareholders.some(
		(s) =>
			s.address &&
			(s.address.includes("香港") ||
				s.address.includes("台灣") ||
				s.address.includes("Hong Kong") ||
				s.address.includes("Taiwan")),
	);

	// Determine business type based on business objects
	const businessType = data.company_info.business_objects.includes("飲食")
		? "飲食服務業"
		: data.company_info.business_objects.includes("配送")
			? "物流配送業"
			: "一般商業";

	const cards = [
		{
			title: "公司名稱",
			value: data.company_info.current_name.zh,
			subtitle: data.company_info.current_name.pt,
			icon: Building,
			color: "blue",
		},
		{
			title: "登記編號",
			value: data.company_info.registration_number,
			subtitle: `申請編號: ${data.company_info.application_number}`,
			icon: FileText,
			color: "emerald",
		},
		{
			title: "現行資本",
			value: `${(data.company_info.current_capital || 0).toLocaleString()} ${data.company_info.currency}`,
			subtitle: `${data.company_info.total_quota_transfers} 次股權變動`,
			icon: DollarSign,
			color: "orange",
		},
		{
			title: "股東結構",
			value: `${activeShareholdersCount} 位現任`,
			subtitle: `共 ${totalHistoricalShareholders} 位歷史股東`,
			icon: Users,
			color: "purple",
		},
		{
			title: "資料日期",
			value: data.company_info.report_as_of_date,
			subtitle: `列印: ${data.company_info.print_date}`,
			icon: Calendar,
			color: "pink",
		},
		{
			title: "業務範圍",
			value: `${data.company_info.business_objects.length} 大領域`,
			subtitle: data.company_info.business_objects.slice(0, 2).join("、"),
			icon: TrendingUp,
			color: "indigo",
		},
		{
			title: "管理狀況",
			value: `${data.current_administrators.length} 位管理員`,
			subtitle: `${data.company_info.total_administrator_changes} 次變更`,
			icon: Clock,
			color: "green",
		},
		{
			title: "企業类型",
			value: hasInternationalShareholder ? "國際合資" : "本地企業",
			subtitle: businessType,
			icon: Globe,
			color: "cyan",
		},
	];

	const colorClasses = {
		blue: "text-blue-600 bg-blue-100",
		emerald: "text-emerald-600 bg-emerald-100",
		orange: "text-orange-600 bg-orange-100",
		purple: "text-purple-600 bg-purple-100",
		pink: "text-pink-600 bg-pink-100",
		indigo: "text-indigo-600 bg-indigo-100",
		green: "text-green-600 bg-green-100",
		cyan: "text-cyan-600 bg-cyan-100",
	};

	return (
		<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
			{cards.map((card) => {
				const Icon = card.icon;
				return (
					<div
						key={card.title}
						className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-200 border border-gray-100 hover:border-gray-200 group"
					>
						<div className="flex items-center justify-between mb-4">
							<div
								className={`p-2 rounded-lg ${colorClasses[card.color as keyof typeof colorClasses]} group-hover:scale-110 transition-transform duration-200`}
							>
								<Icon className="h-5 w-5" />
							</div>
						</div>
						<h3 className="text-sm font-medium text-gray-500 mb-1">
							{card.title}
						</h3>
						<p className="text-lg font-bold text-gray-900 leading-tight mb-1">
							{card.value}
						</p>
						{card.subtitle && (
							<p className="text-xs text-gray-400 leading-relaxed">
								{card.subtitle}
							</p>
						)}
					</div>
				);
			})}
		</div>
	);
}
