import { Credit<PERSON>ard, Heart, Home, MapPin, User, Users } from "lucide-react";
import { useEffect } from "react";
import { useShareholderPopup } from "../contexts/ShareholderPopupContext";

export function ShareholderPopup() {
	const { popupInfo, isVisible, hidePopup } = useShareholderPopup();

	// Handle clicking outside to close popup
	useEffect(() => {
		const handleClickOutside = (_event: MouseEvent) => {
			if (isVisible) {
				hidePopup();
			}
		};

		if (isVisible) {
			document.addEventListener("click", handleClickOutside);
		}

		return () => {
			document.removeEventListener("click", handleClickOutside);
		};
	}, [isVisible, hidePopup]);

	if (!isVisible || !popupInfo) return null;

	// Position popup at right bottom corner
	const getPopupStyle = () => {
		const padding = 20;

		return {
			position: "fixed" as const,
			right: `${padding}px`,
			bottom: `${padding}px`,
			left: "auto",
			top: "auto",
			margin: "0",
			maxWidth: "320px",
			maxHeight: "none",
			width: "320px",
			height: "auto",
			zIndex: 9999,
			transform: isVisible ? "scale(1)" : "scale(0.95)",
			opacity: isVisible ? 1 : 0,
			transition: "all 0.15s ease-out",
		};
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter" || e.key === " ") {
			e.stopPropagation();
		}
	};

	return (
		<dialog
			open
			style={getPopupStyle()}
			className="bg-white rounded-xl shadow-2xl border border-gray-200 p-6 w-80 max-w-sm !fixed !right-5 !bottom-5 !left-auto !top-auto !m-0"
			onClick={(e) => e.stopPropagation()}
			onKeyDown={handleKeyDown}
		>
			{/* Header */}
			<div className="flex items-center space-x-3 mb-4 pb-3 border-b border-gray-100">
				<div
					className={`w-10 h-10 rounded-full flex items-center justify-center ${
						popupInfo.has_no_data
							? "bg-gray-100"
							: popupInfo.is_active
								? "bg-green-100"
								: "bg-red-100"
					}`}
				>
					<User
						className={`h-5 w-5 ${
							popupInfo.has_no_data
								? "text-gray-400"
								: popupInfo.is_active
									? "text-green-600"
									: "text-red-600"
						}`}
					/>
				</div>
				<div>
					<h3 className="font-bold text-gray-900 text-lg">
						{popupInfo.name_zh ||
							(typeof popupInfo.name === "string"
								? popupInfo.name
								: popupInfo.name?.full)}
					</h3>
					{popupInfo.name_pt && (
						<p className="text-sm text-gray-500">{popupInfo.name_pt}</p>
					)}
					{/* Status indicator */}
					{!popupInfo.has_no_data && (
						<p
							className={`text-xs font-medium ${
								popupInfo.is_active ? "text-green-600" : "text-red-600"
							}`}
						>
							{popupInfo.is_active ? "現任股東" : "已退出股東"}
						</p>
					)}
					{popupInfo.has_no_data && (
						<p className="text-xs font-medium text-gray-500">無可用資料</p>
					)}
				</div>
			</div>

			{/* Information Grid */}
			<div className="space-y-3">
				{/* Show "no data" message if no information available */}
				{popupInfo.has_no_data ? (
					<div className="text-center py-4">
						<p className="text-gray-500 text-sm">此人員暫無詳細資料記錄</p>
					</div>
				) : (
					<>
						{/* 股額 */}
						<div className="flex items-center space-x-3">
							<CreditCard className="h-4 w-4 text-green-600 flex-shrink-0" />
							<div>
								<span className="text-sm font-medium text-gray-700">
									目前股額:
								</span>
								<span className="ml-2 text-sm text-gray-900">
									{popupInfo.current_stake !== null &&
									popupInfo.current_stake !== undefined
										? `${popupInfo.current_stake.toLocaleString()} ${popupInfo.currency || "MOP"}`
										: "無資料"}
									{popupInfo.percentage !== null &&
										popupInfo.percentage !== undefined &&
										(popupInfo.current_stake || 0) > 0 && (
											<span className="text-gray-500 ml-1">
												({popupInfo.percentage.toFixed(1)}%)
											</span>
										)}
								</span>
							</div>
						</div>

						{/* Historical stake for exited shareholders */}
						{!popupInfo.is_active &&
							popupInfo.historical_stake &&
							popupInfo.historical_stake > 0 && (
								<div className="flex items-center space-x-3">
									<CreditCard className="h-4 w-4 text-gray-400 flex-shrink-0" />
									<div>
										<span className="text-sm font-medium text-gray-700">
											歷史股額:
										</span>
										<span className="ml-2 text-sm text-gray-600">
											{popupInfo.historical_stake.toLocaleString()}{" "}
											{popupInfo.currency || "MOP"}
										</span>
									</div>
								</div>
							)}

						{/* 性別 */}
						{popupInfo.gender && (
							<div className="flex items-center space-x-3">
								<Users className="h-4 w-4 text-purple-600 flex-shrink-0" />
								<div>
									<span className="text-sm font-medium text-gray-700">
										性別:
									</span>
									<span className="ml-2 text-sm text-gray-900">
										{popupInfo.gender}
									</span>
								</div>
							</div>
						)}

						{/* 婚姻狀況 */}
						{popupInfo.marital_status && (
							<div className="flex items-center space-x-3">
								<Heart className="h-4 w-4 text-pink-600 flex-shrink-0" />
								<div>
									<span className="text-sm font-medium text-gray-700">
										婚姻狀況:
									</span>
									<span className="ml-2 text-sm text-gray-900">
										{popupInfo.marital_status}
									</span>
								</div>
							</div>
						)}

						{/* 配偶 */}
						{(popupInfo.spouse_zh || popupInfo.spouse) && (
							<div className="flex items-center space-x-3">
								<Heart className="h-4 w-4 text-red-600 flex-shrink-0" />
								<div>
									<span className="text-sm font-medium text-gray-700">
										配偶:
									</span>
									<span className="ml-2 text-sm text-gray-900 font-medium">
										{popupInfo.spouse_zh ||
											(typeof popupInfo.spouse === "string"
												? popupInfo.spouse
												: popupInfo.spouse?.full)}
									</span>
								</div>
							</div>
						)}

						{/* 財產制度 */}
						{popupInfo.regime && (
							<div className="flex items-center space-x-3">
								<Home className="h-4 w-4 text-orange-600 flex-shrink-0" />
								<div>
									<span className="text-sm font-medium text-gray-700">
										財產制度:
									</span>
									<span className="ml-2 text-sm text-gray-900">
										{popupInfo.regime}
									</span>
								</div>
							</div>
						)}

						{/* 地址 */}
						{popupInfo.address && (
							<div className="flex items-start space-x-3">
								<MapPin className="h-4 w-4 text-blue-600 flex-shrink-0 mt-0.5" />
								<div>
									<span className="text-sm font-medium text-gray-700">
										地址:
									</span>
									<p className="text-xs text-gray-600 mt-1 leading-relaxed">
										{popupInfo.address}
									</p>
								</div>
							</div>
						)}
					</>
				)}
			</div>

			{/* Footer */}
			<div className="mt-4 pt-3 border-t border-gray-100">
				<p className="text-xs text-gray-400 text-center">點擊其他地方關閉</p>
			</div>
		</dialog>
	);
}
