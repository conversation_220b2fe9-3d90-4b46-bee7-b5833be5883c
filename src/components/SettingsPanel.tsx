import {
	<PERSON>ert<PERSON>ircle,
	CheckCircle,
	Eye,
	<PERSON>Off,
	<PERSON>,
	Setting<PERSON>,
	Trash2,
	X,
} from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { type ApiKeyStatus, apiKeyManager } from "../services/api-key-manager";
import { type CacheStats, cacheManager } from "../services/cache-manager";

export interface SettingsPanelProps {
	isOpen: boolean;
	onClose: () => void;
}

export function SettingsPanel({ isOpen, onClose }: SettingsPanelProps) {
	const [apiKey, setApiKey] = useState("");
	const [showApiKey, setShowApiKey] = useState(false);
	const [keyStatus, setKeyStatus] = useState<ApiKeyStatus | null>(null);
	const [isValidating, setIsValidating] = useState(false);
	const [cacheStats, setCacheStats] = useState<CacheStats | null>(null);
	const [isLoadingCache, setIsLoadingCache] = useState(false);

	useEffect(() => {
		if (isOpen) {
			loadCurrentKey();
			loadCacheStats();
		}
	}, [isOpen]);

	const loadCurrentKey = useCallback(() => {
		const currentKey = apiKeyManager.getApiKey();
		if (currentKey) {
			setApiKey(currentKey);
			validateKey(currentKey);
		}
	}, []);

	const loadCacheStats = useCallback(async () => {
		setIsLoadingCache(true);
		try {
			const stats = await cacheManager.getStats();
			setCacheStats(stats);
		} catch (error) {
			console.error("Failed to load cache stats:", error);
		} finally {
			setIsLoadingCache(false);
		}
	}, []);

	const validateKey = useCallback(async (key: string) => {
		if (!key.trim()) {
			setKeyStatus(null);
			return;
		}

		setIsValidating(true);
		try {
			const status = await apiKeyManager.validateApiKey(key);
			setKeyStatus(status);
		} catch (error) {
			setKeyStatus({
				isValid: false,
				isStored: false,
				error: error instanceof Error ? error.message : "驗證失敗",
			});
		} finally {
			setIsValidating(false);
		}
	}, []);

	const handleKeyChange = useCallback((value: string) => {
		setApiKey(value);
		if (value !== apiKeyManager.getApiKey()) {
			setKeyStatus(null);
		}
	}, []);

	const handleSaveKey = useCallback(async () => {
		if (!apiKey.trim()) {
			return;
		}

		setIsValidating(true);
		try {
			const status = await apiKeyManager.validateApiKey(apiKey);
			setKeyStatus(status);

			if (status.isValid) {
				apiKeyManager.setApiKey(apiKey);
			}
		} catch (error) {
			setKeyStatus({
				isValid: false,
				isStored: false,
				error: error instanceof Error ? error.message : "保存失敗",
			});
		} finally {
			setIsValidating(false);
		}
	}, [apiKey]);

	const handleClearKey = useCallback(() => {
		apiKeyManager.clearApiKey();
		setApiKey("");
		setKeyStatus(null);
	}, []);

	const handleClearCache = useCallback(async () => {
		if (confirm("確定要清除所有快取資料嗎？這個操作無法復原。")) {
			try {
				await cacheManager.clear();
				await loadCacheStats();
			} catch (error) {
				console.error("Failed to clear cache:", error);
			}
		}
	}, [loadCacheStats]);

	const formatFileSize = (bytes: number): string => {
		if (bytes === 0) return "0 B";
		const k = 1024;
		const sizes = ["B", "KB", "MB", "GB"];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return `${(bytes / k ** i).toFixed(1)} ${sizes[i]}`;
	};

	if (!isOpen) return null;

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
			<div className="bg-white rounded-2xl shadow-xl max-w-lg w-full max-h-[90vh] overflow-hidden">
				{/* Header */}
				<div className="flex items-center justify-between p-6 border-b border-gray-200">
					<div className="flex items-center space-x-3">
						<Settings className="h-6 w-6 text-gray-600" />
						<h2 className="text-xl font-semibold text-gray-900">設定</h2>
					</div>
					<button
						type="button"
						onClick={onClose}
						className="text-gray-400 hover:text-gray-600 transition-colors"
					>
						<X className="h-6 w-6" />
					</button>
				</div>

				<div className="p-6 space-y-8 overflow-y-auto max-h-[calc(90vh-80px)]">
					{/* API Key Section */}
					<div className="space-y-4">
						<div className="flex items-center space-x-2">
							<Key className="h-5 w-5 text-blue-600" />
							<h3 className="text-lg font-medium text-gray-900">
								OpenRouter API 金鑰
							</h3>
						</div>

						<p className="text-sm text-gray-600">
							請輸入您的 OpenRouter API 金鑰以啟用 PDF 處理功能。OpenRouter 提供更穩定的 Gemini 2.5 Flash 存取。
							<a
								href="https://openrouter.ai/keys"
								target="_blank"
								rel="noopener noreferrer"
								className="text-blue-600 hover:text-blue-700 underline ml-1"
							>
								取得 API 金鑰
							</a>
						</p>

						<div className="space-y-3">
							<div className="relative">
								<input
									type={showApiKey ? "text" : "password"}
									value={apiKey}
									onChange={(e) => handleKeyChange(e.target.value)}
									placeholder="輸入您的 OpenRouter API 金鑰 (sk-or-...)..."
									className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
								/>
								<button
									type="button"
									onClick={() => setShowApiKey(!showApiKey)}
									className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
								>
									{showApiKey ? (
										<EyeOff className="h-5 w-5" />
									) : (
										<Eye className="h-5 w-5" />
									)}
								</button>
							</div>

							{keyStatus && (
								<div
									className={`flex items-start space-x-2 p-3 rounded-lg ${
										keyStatus.isValid
											? "bg-green-50 border border-green-200"
											: "bg-red-50 border border-red-200"
									}`}
								>
									{keyStatus.isValid ? (
										<CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
									) : (
										<AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
									)}
									<div className="flex-1">
										<p
											className={`text-sm font-medium ${
												keyStatus.isValid ? "text-green-800" : "text-red-800"
											}`}
										>
											{keyStatus.isValid ? "API 金鑰有效" : "API 金鑰無效"}
										</p>
										{keyStatus.error && (
											<p className="text-sm text-red-700 mt-1">
												{keyStatus.error}
											</p>
										)}
									</div>
								</div>
							)}

							<div className="flex space-x-3">
								<button
									type="button"
									onClick={handleSaveKey}
									disabled={isValidating || !apiKey.trim()}
									className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white px-4 py-2 rounded-lg font-medium transition-colors"
								>
									{isValidating ? "驗證中..." : "保存金鑰"}
								</button>

								{apiKeyManager.hasApiKey() && (
									<button
										type="button"
										onClick={handleClearKey}
										className="px-4 py-2 border border-red-300 text-red-700 hover:bg-red-50 rounded-lg font-medium transition-colors"
									>
										清除
									</button>
								)}
							</div>
						</div>
					</div>

					{/* Cache Section */}
					<div className="space-y-4">
						<div className="flex items-center space-x-2">
							<Trash2 className="h-5 w-5 text-orange-600" />
							<h3 className="text-lg font-medium text-gray-900">快取管理</h3>
						</div>

						<p className="text-sm text-gray-600">
							管理已處理的 PDF 檔案快取，可提升處理速度並節省 API 使用量。
						</p>

						{isLoadingCache ? (
							<div className="flex items-center justify-center py-8">
								<div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
							</div>
						) : cacheStats ? (
							<div className="space-y-4">
								<div className="grid grid-cols-2 gap-4">
									<div className="bg-gray-50 rounded-lg p-4">
										<p className="text-sm text-gray-600">已快取檔案</p>
										<p className="text-2xl font-bold text-gray-900">
											{cacheStats.totalEntries}
										</p>
									</div>
									<div className="bg-gray-50 rounded-lg p-4">
										<p className="text-sm text-gray-600">佔用空間</p>
										<p className="text-2xl font-bold text-gray-900">
											{formatFileSize(cacheStats.totalSize)}
										</p>
									</div>
								</div>

								{cacheStats.expiredEntries > 0 && (
									<div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
										<p className="text-sm text-yellow-800">
											有 {cacheStats.expiredEntries} 個過期快取檔案
										</p>
									</div>
								)}

								<button
									type="button"
									onClick={handleClearCache}
									className="w-full py-2 px-4 border border-red-300 text-red-700 hover:bg-red-50 rounded-lg font-medium transition-colors"
								>
									清除所有快取
								</button>
							</div>
						) : (
							<div className="text-center py-8 text-gray-500">
								無法載入快取資訊
							</div>
						)}
					</div>
				</div>
			</div>
		</div>
	);
}
