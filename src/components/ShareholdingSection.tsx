import { <PERSON><PERSON>hart<PERSON>, <PERSON>, <PERSON><PERSON>ff, <PERSON><PERSON><PERSON><PERSON>p, Users } from "lucide-react";
import { useState } from "react";
import {
	Bar,
	BarChart,
	CartesianGrid,
	Cell,
	Legend,
	Pie,
	PieChart,
	ResponsiveContainer,
	Tooltip,
	XAxis,
	YAxis,
} from "recharts";
import {
	buildNameChangeMappings,
	businessRegistrationReport,
	getCanonicalName,
	shareholderColorMap,
} from "../data/business-registration-data";

import type {
	LineChartDataPoint,
	PieChartData,
	ShareholdingSectionProps,
} from "../types/component-types";
import { ClickableShareholderName } from "./ClickableShareholderName";

export function ShareholdingSection({
	data,
	expanded = false,
}: ShareholdingSectionProps) {
	const [showHistoricalShareholders, setShowHistoricalShareholders] =
		useState(false);

	const activeShareholders =
		data.active_shareholders ||
		data.shareholders.filter((s) => (s.current_stake || 0) > 0);
	const allShareholders = data.shareholders;
	const historicalShareholders = allShareholders.filter(
		(s) =>
			!activeShareholders.some((active) => {
				const sName =
					typeof s.name === "string"
						? s.name
						: (s.name as { full?: string })?.full || s.name_zh || "";
				const activeName = active.name || active.name_zh || "";
				return activeName === sName;
			}),
	);

	// Use the new shareholding history data
	const shareholdingHistory = data.shareholding_history || [];

	const pieData: PieChartData[] = activeShareholders.map((shareholder) => ({
		name: shareholder.name_zh || shareholder.name,
		value: shareholder.current_stake,
		percentage: Math.round(shareholder.percentage * 10) / 10,
	}));

	// Function to get color for a shareholder name
	const getShareholderColor = (shareholderName: string): string => {
		const displayName = shareholderName.split(" ")[0] || shareholderName;
		return shareholderColorMap.get(displayName) || "#6B7280"; // fallback gray
	};

	// Function to find spouse information from the original business registration data
	const findSpouseInfo = (shareholderName: string): string | null => {
		const nameMap = buildNameChangeMappings();

		// Search through all business registration entries for shareholder data with spouse info
		for (const entry of businessRegistrationReport.inscriptionsAndEndorsements) {
			if ("shareholders" in entry && entry.shareholders) {
				for (const shareholder of entry.shareholders) {
					// Check if this shareholder matches our name (considering name changes)
					const canonicalName = getCanonicalName(
						shareholder.name.full,
						nameMap,
					);
					const displayName = canonicalName.split(" ")[0] || canonicalName;
					const currentDisplayName =
						shareholder.name.chinese || shareholder.name.full.split(" ")[0];

					if (
						displayName === shareholderName ||
						currentDisplayName === shareholderName ||
						shareholder.name.full.includes(shareholderName) ||
						shareholderName.includes(currentDisplayName) ||
						canonicalName.includes(shareholderName)
					) {
						if (shareholder.spouse?.chinese) {
							return shareholder.spouse.chinese;
						}
						if (shareholder.spouse?.full) {
							return (
								shareholder.spouse.full.split(" ")[0] || shareholder.spouse.full
							);
						}
					}
				}
			}

			// Check active parties in quota transfers
			if ("activeParties" in entry && entry.activeParties) {
				for (const party of entry.activeParties) {
					const canonicalName = getCanonicalName(party.name.full, nameMap);
					const displayName = canonicalName.split(" ")[0] || canonicalName;
					const currentDisplayName =
						party.name.chinese || party.name.full.split(" ")[0];

					if (
						displayName === shareholderName ||
						currentDisplayName === shareholderName ||
						party.name.full.includes(shareholderName) ||
						shareholderName.includes(currentDisplayName) ||
						canonicalName.includes(shareholderName)
					) {
						if (party.spouse?.chinese) {
							return party.spouse.chinese;
						}
						if (party.spouse?.full) {
							return party.spouse.full.split(" ")[0] || party.spouse.full;
						}
					}
				}
			}
		}

		return null;
	};

	// Process shareholding history data for visualization
	const processHistoricalData = (): LineChartDataPoint[] => {
		if (!shareholdingHistory || shareholdingHistory.length === 0) return [];

		// Convert shareholding history to chart format
		return shareholdingHistory.map((entry) => {
			const dataPoint: LineChartDataPoint = {
				date: new Date(entry.date).getFullYear().toString(),
			};

			// Add each shareholder's percentage to the data point
			for (const sh of entry.shareholders) {
				dataPoint[sh.name] = sh.percentage;
			}

			return dataPoint;
		});
	};

	const historicalChartData = processHistoricalData();

	// Get unique shareholders from shareholding history
	const uniqueShareholders =
		shareholdingHistory.length > 0
			? [
					...new Set(
						shareholdingHistory.flatMap((h) =>
							h.shareholders.map((s) => s.name),
						),
					),
				]
			: [
					...new Set(
						data.shareholders.map(
							(s) =>
								s.name_zh ||
								(typeof s.name === "string"
									? s.name
									: (s.name as { full?: string })?.full || ""),
						),
					),
				];

	interface TooltipProps {
		active?: boolean;
		payload?: Array<{
			payload: PieChartData;
		}>;
	}

	const CustomTooltip = ({ active, payload }: TooltipProps) => {
		if (active && payload && payload.length) {
			const data = payload[0].payload;
			return (
				<div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
					<p className="font-medium">{data.name}</p>
					<p className="text-sm text-gray-600">
						持股: {data.value ? data.value.toLocaleString() : "0"} (
						{data.percentage || "0"}%)
					</p>
				</div>
			);
		}
		return null;
	};

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<h2 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
					<Users className="h-6 w-6 text-blue-600" />
					<span>股權結構與變動</span>
				</h2>
			</div>

			<div className="grid lg:grid-cols-2 gap-8">
				{/* Pie Chart */}
				<div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
					<h3 className="text-lg font-semibold text-gray-900 mb-4">股權分布</h3>
					<div className="h-80">
						<ResponsiveContainer width="100%" height="100%">
							<PieChart>
								<Pie
									data={pieData}
									cx="50%"
									cy="50%"
									labelLine={false}
									outerRadius={100}
									fill="#8884d8"
									dataKey="value"
									label={({ percentage }) => `${percentage}%`}
								>
									{pieData.map((item) => (
										<Cell
											key={`cell-${item.name}`}
											fill={getShareholderColor(item.name)}
										/>
									))}
								</Pie>
								<Tooltip content={<CustomTooltip />} />
								<Legend />
							</PieChart>
						</ResponsiveContainer>
					</div>
				</div>

				{/* Current Shareholders */}
				<div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
					<h3 className="text-lg font-semibold text-gray-900 mb-4">現任股東</h3>
					<div className="space-y-4">
						{activeShareholders.map((shareholder) => (
							<div
								key={shareholder.id}
								className="border border-gray-200 rounded-lg p-4"
							>
								<div className="flex items-center justify-between mb-2">
									<h4 className="font-medium text-gray-900">
										<ClickableShareholderName
											shareholderName={shareholder.name}
										>
											{shareholder.name}
										</ClickableShareholderName>
									</h4>
									<div className="flex items-center space-x-2">
										<div
											className="w-3 h-3 rounded-full"
											style={{
												backgroundColor: getShareholderColor(shareholder.name),
											}}
										/>
										<span className="text-sm font-medium text-gray-700">
											{(
												(shareholder.current_stake /
													data.company_info.current_capital) *
												100
											).toFixed(1)}
											%
										</span>
									</div>
								</div>
								<div className="text-sm text-gray-600 space-y-1">
									<p>
										股額: {shareholder.current_stake.toLocaleString()}{" "}
										{data.company_info.currency}
									</p>
									{/* Find the full shareholder data for detailed info */}
									{(() => {
										const fullShareholderData = data.shareholders.find(
											(s) =>
												(typeof s.name === "string"
													? s.name
													: (s.name as { full?: string })?.full) ===
													shareholder.name ||
												s.name_zh === shareholder.name ||
												(s as { id?: string }).id === shareholder.id,
										);
										if (fullShareholderData) {
											return (
												<>
													<p>
														性別: {fullShareholderData.gender || "未知"} |
														婚姻狀況:{" "}
														{fullShareholderData.marital_status || "未知"}
													</p>
													{fullShareholderData.spouse && (
														<p>
															配偶:{" "}
															<ClickableShareholderName
																shareholderName={
																	fullShareholderData.spouse_zh ||
																	(typeof fullShareholderData.spouse ===
																	"string"
																		? fullShareholderData.spouse
																		: fullShareholderData.spouse?.full || "")
																}
															>
																{fullShareholderData.spouse_zh ||
																	(typeof fullShareholderData.spouse ===
																	"string"
																		? fullShareholderData.spouse
																		: fullShareholderData.spouse?.full)}
															</ClickableShareholderName>
														</p>
													)}
													{fullShareholderData.regime && (
														<p>財產制度: {fullShareholderData.regime}</p>
													)}
													{fullShareholderData.address && (
														<p className="text-xs text-gray-500">
															地址:{" "}
															{fullShareholderData.address.length > 30
																? `${fullShareholderData.address.substring(0, 30)}...`
																: fullShareholderData.address}
														</p>
													)}
												</>
											);
										}
										return <p>性別: 未知 | 婚姻狀況: 未知</p>;
									})()}
								</div>
							</div>
						))}
					</div>
				</div>
			</div>

			{/* Historical Shareholding Chart */}
			<div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
				<div className="flex items-center justify-between mb-4">
					<h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
						<BarChart3 className="h-5 w-5 text-blue-600" />
						<span>歷史股權變化</span>
					</h3>
					<div className="text-sm text-gray-500">2018-2024 年度股權分布</div>
				</div>

				<div className="h-96 mb-4">
					<ResponsiveContainer width="100%" height="100%">
						<BarChart
							data={historicalChartData}
							margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
						>
							<CartesianGrid strokeDasharray="3 3" />
							<XAxis
								dataKey="date"
								tick={{ fontSize: 12 }}
								axisLine={{ stroke: "#E5E7EB" }}
							/>
							<YAxis
								tick={{ fontSize: 12 }}
								axisLine={{ stroke: "#E5E7EB" }}
								label={{
									value: "持股比例 (%)",
									angle: -90,
									position: "insideLeft",
								}}
							/>
							<Tooltip
								content={({ active, payload, label }) => {
									if (active && payload && payload.length) {
										// Filter out entries with 0% to avoid confusion
										const nonZeroEntries = payload.filter(
											(entry: {
												dataKey: string;
												value: number;
												color: string;
											}) => entry.value > 0,
										);

										if (nonZeroEntries.length === 0) return null;

										return (
											<div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
												<p className="font-medium mb-2">{label} 年</p>
												{nonZeroEntries.map(
													(entry: {
														dataKey: string;
														value: number;
														color: string;
													}) => (
														<p
															key={entry.dataKey}
															className="text-sm"
															style={{ color: entry.color }}
														>
															{entry.dataKey}:{" "}
															{Number(entry.value)
																.toFixed(3)
																.replace(/\.?0+$/, "")}
															%
														</p>
													),
												)}
											</div>
										);
									}
									return null;
								}}
							/>
							<Legend />
							{(uniqueShareholders as string[]).map((shareholder: string) => (
								<Bar
									key={shareholder}
									dataKey={shareholder}
									stackId="shareholding"
									fill={getShareholderColor(shareholder)}
								/>
							))}
						</BarChart>
					</ResponsiveContainer>
				</div>

				<div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 text-sm">
					{(uniqueShareholders as string[]).map((shareholder: string) => {
						// More comprehensive search for shareholder data (including historical names)
						const shareholderData = data.shareholders.find((s) => {
							const sName =
								typeof s.name === "string"
									? s.name
									: (s.name as { full?: string })?.full || "";
							const names = [
								sName,
								s.name_zh,
								s.name_pt,
								sName?.split(" ")[0],
								s.name_zh?.split(" ")[0],
							].filter(Boolean);
							return names.some(
								(name) =>
									name === shareholder ||
									(typeof name === "string" && name.includes(shareholder)) ||
									(typeof name === "string" && shareholder?.includes(name)),
							);
						});

						const activeShareholderData = data.active_shareholders?.find(
							(s) => {
								const names = [
									s.name,
									s.name_zh,
									s.name_pt,
									s.name?.split(" ")[0],
									s.name_zh?.split(" ")[0],
								].filter(Boolean);
								return names.some(
									(name) =>
										name === shareholder ||
										(typeof name === "string" && name.includes(shareholder)) ||
										(typeof name === "string" && shareholder?.includes(name)),
								);
							},
						);

						const isActive =
							(shareholderData?.current_stake ?? 0) > 0 ||
							(activeShareholderData?.current_stake ?? 0) > 0;
						return (
							<div key={shareholder} className="flex flex-col space-y-1">
								<div className="flex items-center space-x-2">
									<div
										className="w-3 h-3 rounded-full"
										style={{
											backgroundColor: getShareholderColor(shareholder),
										}}
									/>
									<span
										className={`${isActive ? "text-gray-900 font-medium" : "text-gray-500"}`}
									>
										<ClickableShareholderName shareholderName={shareholder}>
											{shareholder}
										</ClickableShareholderName>
									</span>
									{!isActive && (
										<span className="text-xs text-red-500 bg-red-50 px-1 rounded">
											已退出
										</span>
									)}
								</div>
								{/* Add spouse badge if available (for both active and exited shareholders) */}
								{(() => {
									// Try to find spouse information from multiple sources
									let spouseName =
										shareholderData?.spouse_zh ||
										shareholderData?.spouse ||
										activeShareholderData?.spouse_zh ||
										activeShareholderData?.spouse;

									// If not found in current data structures, search the original business registration data
									if (!spouseName) {
										const foundSpouse = findSpouseInfo(shareholder);
										spouseName = foundSpouse || undefined;
									}

									if (spouseName) {
										const spouseDisplayName =
											typeof spouseName === "string"
												? spouseName
												: (spouseName as { full?: string; chinese?: string })
														?.full ||
													(spouseName as { full?: string; chinese?: string })
														?.chinese ||
													"";
										return (
											<div className="ml-5">
												<span
													className={`text-xs px-2 py-0.5 rounded-full ${
														isActive
															? "text-blue-600 bg-blue-50"
															: "text-gray-500 bg-gray-100"
													}`}
												>
													配偶:{" "}
													<ClickableShareholderName
														shareholderName={spouseDisplayName}
													>
														{spouseDisplayName}
													</ClickableShareholderName>
												</span>
											</div>
										);
									}

									return null;
								})()}
							</div>
						);
					})}
				</div>
			</div>

			{/* Historical Shareholders Details */}
			{historicalShareholders.length > 0 && (
				<div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
					<div className="flex items-center justify-between mb-4">
						<h3 className="text-lg font-semibold text-gray-900">
							歷史股東詳情
						</h3>
						<button
							type="button"
							onClick={() =>
								setShowHistoricalShareholders(!showHistoricalShareholders)
							}
							className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 transition-colors"
						>
							{showHistoricalShareholders ? (
								<>
									<EyeOff className="h-4 w-4" />
									<span>隱藏</span>
								</>
							) : (
								<>
									<Eye className="h-4 w-4" />
									<span>查看詳情</span>
								</>
							)}
						</button>
					</div>

					{showHistoricalShareholders && (
						<div className="space-y-4">
							{historicalShareholders.map((shareholder, index) => {
								const shareholderName =
									typeof shareholder.name === "string"
										? shareholder.name
										: (shareholder.name as { full?: string })?.full ||
											shareholder.name_zh ||
											"";
								const shareholderKey =
									(shareholder as { id?: string }).id ||
									shareholderName ||
									index;
								return (
									<div
										key={shareholderKey}
										className="border border-gray-200 rounded-lg p-4 bg-gray-50"
									>
										<div className="flex items-center justify-between mb-2">
											<h4 className="font-medium text-gray-700">
												<ClickableShareholderName
													shareholderName={shareholderName}
												>
													{shareholderName}
												</ClickableShareholderName>
											</h4>
											<span className="text-sm text-red-600 font-medium">
												已退出
											</span>
										</div>
										<div className="text-sm text-gray-600 space-y-1">
											<p>
												性別: {shareholder.gender} | 婚姻狀況:{" "}
												{shareholder.marital_status}
											</p>
											<p>
												最後持股:{" "}
												{(shareholder.current_stake || 0).toLocaleString()}{" "}
												{data.company_info.currency}
											</p>
											{shareholder.address && (
												<p className="text-xs text-gray-500">
													地址: {shareholder.address}
												</p>
											)}
										</div>
									</div>
								);
							})}
						</div>
					)}
				</div>
			)}

			{/* Shareholding Timeline */}
			{expanded && (
				<div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
					<h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
						<TrendingUp className="h-5 w-5 text-blue-600" />
						<span>股權變動時間軸</span>
					</h3>
					<div className="space-y-4">
						{data.history
							.filter(
								(event) =>
									event.subject === "quota_transfer" ||
									event.subject === "quota_acquisition" ||
									event.subject === "bylaws_amendment",
							)
							.map((event) => (
								<div
									key={event.event_id}
									className="flex items-start space-x-4"
								>
									<div className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2" />
									<div className="flex-grow">
										<div className="flex items-center justify-between">
											<h4 className="font-medium text-gray-900">
												{event.type}
											</h4>
											<span className="text-sm text-gray-500">
												{event.date}
											</span>
										</div>
										<div className="text-sm text-gray-600 mt-1">
											{event.subject === "quota_transfer" &&
												event.details &&
												Array.isArray(
													(event.details as { activeParties?: unknown[] })
														.activeParties,
												) && (
													<p>
														轉讓方:{" "}
														{(
															event.details as {
																activeParties: Array<{
																	name: { full?: string; chinese?: string };
																}>;
															}
														).activeParties.map((p, index: number) => (
															<span key={p.name.full || `party-${index}`}>
																<ClickableShareholderName
																	shareholderName={
																		(p.name.full || "").split(" ")[0] ||
																		p.name.full ||
																		""
																	}
																>
																	{p.name.full || p.name.chinese || ""}
																</ClickableShareholderName>
																{index <
																	((
																		event.details as {
																			activeParties?: unknown[];
																		}
																	).activeParties?.length || 0) -
																		1 && ", "}
															</span>
														))}
													</p>
												)}
											{event.subject === "quota_transfer" &&
												event.details &&
												Array.isArray(
													(event.details as { passiveParties?: unknown[] })
														.passiveParties,
												) && (
													<p>
														受讓方:{" "}
														{(
															event.details as {
																passiveParties: Array<{
																	shareholder: {
																		full?: string;
																		chinese?: string;
																	};
																}>;
															}
														).passiveParties.map((p, index: number) => (
															<span
																key={p.shareholder.full || `passive-${index}`}
															>
																<ClickableShareholderName
																	shareholderName={
																		(p.shareholder.full || "").split(" ")[0] ||
																		p.shareholder.full ||
																		""
																	}
																>
																	{p.shareholder.full ||
																		p.shareholder.chinese ||
																		""}
																</ClickableShareholderName>
																{index <
																	((
																		event.details as {
																			passiveParties?: unknown[];
																		}
																	).passiveParties?.length || 0) -
																		1 && ", "}
															</span>
														))}
													</p>
												)}
											{event.subject === "bylaws_amendment" &&
												event.details &&
												(
													event.details as {
														alterations?: { capital?: { amount: number } };
													}
												).alterations?.capital && (
													<p>
														資本變更:{" "}
														{(
															event.details as {
																alterations: { capital: { amount: number } };
															}
														).alterations.capital.amount.toLocaleString()}{" "}
														{data.company_info.currency}
													</p>
												)}
										</div>
									</div>
								</div>
							))}
					</div>
				</div>
			)}
		</div>
	);
}
