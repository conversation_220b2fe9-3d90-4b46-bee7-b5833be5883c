import { AlertCircle, CheckCircle, FileText, Upload, X } from "lucide-react";
import { useCallback, useState } from "react";
import { apiKeyManager } from "../services/api-key-manager";
import {
	type ProcessingProgress,
	type ProcessingResult,
	pdfProcessor,
} from "../services/pdf-processor";

export interface PdfUploadProps {
	onProcessingComplete: (result: ProcessingResult) => void;
	onProcessingStart?: () => void;
	className?: string;
}

export function PdfUpload({
	onProcessingComplete,
	onProcessingStart,
	className = "",
}: PdfUploadProps) {
	const [isDragOver, setIsDragOver] = useState(false);
	const [isProcessing, setIsProcessing] = useState(false);
	const [progress, setProgress] = useState<ProcessingProgress | null>(null);
	const [error, setError] = useState<string | null>(null);
	const [file, setFile] = useState<File | null>(null);

	const validateFile = useCallback((file: File): string | null => {
		if (file.type !== "application/pdf") {
			return "只支援 PDF 檔案格式";
		}
		if (file.size > 10 * 1024 * 1024) {
			return "檔案大小不能超過 10MB";
		}
		return null;
	}, []);

	const processFile = useCallback(
		async (file: File) => {
			if (!apiKeyManager.hasApiKey()) {
				setError("請先設定 Gemini API 金鑰");
				return;
			}

			const validationError = validateFile(file);
			if (validationError) {
				setError(validationError);
				return;
			}

			setFile(file);
			setError(null);
			setIsProcessing(true);
			onProcessingStart?.();

			try {
				const result = await pdfProcessor.processFile(file, (progress) => {
					setProgress(progress);
				});

				if (result.success) {
					setProgress({
						stage: "completed",
						message: result.fromCache ? "已從快取載入" : "處理完成",
						progress: 100,
					});
					onProcessingComplete(result);
				} else {
					setError(result.error || "處理失敗");
					setIsProcessing(false);
				}
			} catch (error) {
				setError(error instanceof Error ? error.message : "未知錯誤");
				setIsProcessing(false);
			}
		},
		[onProcessingComplete, onProcessingStart, validateFile],
	);

	const handleDrop = useCallback(
		(e: React.DragEvent) => {
			e.preventDefault();
			setIsDragOver(false);

			const files = Array.from(e.dataTransfer.files);
			if (files.length > 0) {
				processFile(files[0]);
			}
		},
		[processFile],
	);

	const handleFileSelect = useCallback(
		(e: React.ChangeEvent<HTMLInputElement>) => {
			const files = Array.from(e.target.files || []);
			if (files.length > 0) {
				processFile(files[0]);
			}
		},
		[processFile],
	);

	const handleDragOver = useCallback((e: React.DragEvent) => {
		e.preventDefault();
		setIsDragOver(true);
	}, []);

	const handleDragLeave = useCallback((e: React.DragEvent) => {
		e.preventDefault();
		setIsDragOver(false);
	}, []);

	const resetUpload = useCallback(() => {
		setIsProcessing(false);
		setProgress(null);
		setError(null);
		setFile(null);
	}, []);

	const getProgressColor = () => {
		if (progress?.stage === "error") return "text-red-600";
		if (progress?.stage === "completed") return "text-green-600";
		return "text-blue-600";
	};

	const getProgressBarColor = () => {
		if (progress?.stage === "error") return "bg-red-500";
		if (progress?.stage === "completed") return "bg-green-500";
		return "bg-blue-500";
	};

	return (
		<div className={`max-w-md mx-auto ${className}`}>
			{!isProcessing ? (
				<div className="space-y-4">
					<label
						className={`relative block border-2 border-dashed rounded-2xl p-12 transition-all duration-300 cursor-pointer group w-full ${
							isDragOver
								? "border-blue-500 bg-blue-50"
								: error
									? "border-red-300 bg-red-50"
									: "border-gray-300 bg-white hover:border-blue-400 hover:bg-blue-50/50"
						}`}
						onDragOver={handleDragOver}
						onDragLeave={handleDragLeave}
						onDrop={handleDrop}
					>
						<input
							type="file"
							accept=".pdf"
							onChange={handleFileSelect}
							className="sr-only"
							disabled={isProcessing}
						/>

						<div className="space-y-4 text-center">
							{error ? (
								<AlertCircle className="h-12 w-12 text-red-400 mx-auto" />
							) : (
								<Upload className="h-12 w-12 text-gray-400 mx-auto group-hover:text-blue-500 transition-colors" />
							)}

							<div className="space-y-2">
								<p className="text-lg font-medium text-gray-700">
									{error ? "上傳失敗" : "上傳商業登記報告"}
								</p>
								<p className="text-sm text-gray-500">
									拖曳PDF檔案至此處或點擊選擇檔案
								</p>
								<p className="text-xs text-gray-400">
									支援格式：PDF (最大 10MB)
								</p>
							</div>
						</div>
					</label>

					{error && (
						<div className="bg-red-50 border border-red-200 rounded-lg p-4">
							<div className="flex items-start space-x-3">
								<AlertCircle className="h-5 w-5 text-red-400 mt-0.5" />
								<div className="flex-1">
									<p className="text-sm font-medium text-red-800">錯誤</p>
									<p className="text-sm text-red-700 mt-1">{error}</p>
								</div>
								<button
									type="button"
									onClick={resetUpload}
									className="text-red-400 hover:text-red-600"
								>
									<X className="h-5 w-5" />
								</button>
							</div>
						</div>
					)}
				</div>
			) : (
				<div className="bg-white rounded-2xl border-2 border-gray-200 p-8">
					<div className="space-y-6">
						{/* File Info */}
						{file && (
							<div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
								<FileText className="h-8 w-8 text-blue-500" />
								<div className="flex-1">
									<p className="font-medium text-gray-900 truncate">
										{file.name}
									</p>
									<p className="text-sm text-gray-500">
										{(file.size / (1024 * 1024)).toFixed(2)} MB
									</p>
								</div>
							</div>
						)}

						{/* Progress Bar */}
						{progress && (
							<div className="space-y-3">
								<div className="flex items-center justify-between">
									<span className={`text-sm font-medium ${getProgressColor()}`}>
										{progress.message}
									</span>
									<span className="text-sm text-gray-500">
										{progress.progress}%
									</span>
								</div>

								<div className="w-full bg-gray-200 rounded-full h-2">
									<div
										className={`h-2 rounded-full transition-all duration-300 ${getProgressBarColor()}`}
										style={{ width: `${progress.progress}%` }}
									/>
								</div>

								{progress.details && (
									<p className="text-xs text-gray-500 text-center">
										{progress.details}
									</p>
								)}

								{progress.stage === "completed" && (
									<div className="flex items-center justify-center space-x-2 text-green-600">
										<CheckCircle className="h-5 w-5" />
										<span className="text-sm font-medium">處理完成！</span>
									</div>
								)}
							</div>
						)}

						{/* Cancel Button */}
						{progress &&
							progress.stage !== "completed" &&
							progress.stage !== "error" && (
								<button
									type="button"
									onClick={resetUpload}
									className="w-full py-2 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
								>
									取消處理
								</button>
							)}
					</div>
				</div>
			)}
		</div>
	);
}
