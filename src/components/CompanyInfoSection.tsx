import {
	Briefcase,
	Building,
	Clock,
	DollarSign,
	Eye,
	EyeOff,
	MapPin,
	Users,
} from "lucide-react";
import { useState } from "react";
import type {
	CompanyInfoSectionProps,
	HistoryToggleState,
	LatestCompanyInfo,
} from "../types/component-types";
import { ClickableShareholderName } from "./ClickableShareholderName";

export function CompanyInfoSection({
	data,
	showTimeline = false,
}: CompanyInfoSectionProps) {
	const [showHistory, setShowHistory] = useState<HistoryToggleState>({});
	const [showAllHistory, setShowAllHistory] = useState(false);

	const toggleHistory = (section: string) => {
		setShowHistory((prev) => ({ ...prev, [section]: !prev[section] }));
	};

	const toggleAllHistory = () => {
		const newState = !showAllHistory;
		setShowAllHistory(newState);
		setShowHistory({
			address: newState,
			business: newState,
			capital: newState,
			admin: newState,
		});
	};

	const getLatestInfo = (): LatestCompanyInfo => {
		return {
			address: data.company_info.current_address,
			businessScope:
				data.company_info.business_objects?.join("、") ||
				data.company_info.business_scope ||
				"",
			capital: data.company_info.current_capital,
		};
	};

	const info = getLatestInfo();

	if (showTimeline) {
		return (
			<div className="space-y-6">
				<div className="flex items-center justify-between">
					<h2 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
						<Clock className="h-6 w-6 text-blue-600" />
						<span>完整變動記錄</span>
					</h2>
				</div>

				<div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
					<div className="space-y-6">
						{data.history.map((event, index: number) => (
							<div key={event.event_id} className="relative">
								{index !== data.history.length - 1 && (
									<div className="absolute left-4 top-8 w-0.5 h-full bg-gray-200" />
								)}
								<div className="flex items-start space-x-4">
									<div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
										{index + 1}
									</div>
									<div className="flex-grow">
										<div className="bg-gray-50 rounded-lg p-4">
											<div className="flex items-center justify-between mb-2">
												<h3 className="font-semibold text-gray-900">
													{event.type}
												</h3>
												<span className="text-sm text-gray-500">
													{event.date}
												</span>
											</div>
											<div className="text-sm text-gray-600 space-y-1">
												{"name" in event.details && (
													<p>
														<strong>公司名稱:</strong>{" "}
														{(event.details as { name?: { zh?: string } }).name
															?.zh || ""}
													</p>
												)}
												{"address" in event.details && (
													<p>
														<strong>法人住所:</strong>{" "}
														{String(
															(event.details as { address?: string }).address ||
																"",
														)}
													</p>
												)}
												{"new_address" in event.details && (
													<p>
														<strong>新址:</strong>{" "}
														{String(
															(event.details as { new_address?: string })
																.new_address || "",
														)}
													</p>
												)}
												{"business_scope" in event.details && (
													<p>
														<strong>營業範圍:</strong>{" "}
														{String(
															(event.details as { business_scope?: string })
																.business_scope || "",
														)}
													</p>
												)}
												{"capital" in event.details && (
													<p>
														<strong>資本額:</strong>{" "}
														{(
															(event.details as { capital?: number }).capital ||
															0
														).toLocaleString()}{" "}
														{data.company_info.currency}
													</p>
												)}
												{"new_capital" in event.details && (
													<p>
														<strong>新資本額:</strong>{" "}
														{(
															(event.details as { new_capital?: number })
																.new_capital || 0
														).toLocaleString()}{" "}
														{data.company_info.currency}
													</p>
												)}
												{"documents" in event.details && (
													<p>
														<strong>相關文件:</strong>{" "}
														{(
															(event.details as { documents?: string[] })
																.documents || []
														).join(", ")}
													</p>
												)}
											</div>
										</div>
									</div>
								</div>
							</div>
						))}
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<h2 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
					<Building className="h-6 w-6 text-blue-600" />
					<span>公司資訊</span>
				</h2>
				<button
					type="button"
					onClick={toggleAllHistory}
					className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 transition-colors"
				>
					{showAllHistory ? (
						<EyeOff className="h-4 w-4" />
					) : (
						<Eye className="h-4 w-4" />
					)}
					<span>{showAllHistory ? "隱藏所有歷史" : "顯示所有歷史"}</span>
				</button>
			</div>

			<div className="grid md:grid-cols-2 gap-6">
				{/* Basic Information */}
				<div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
					<h3 className="text-lg font-semibold text-gray-900 mb-4">基本資料</h3>
					<div className="space-y-4">
						<div className="flex items-start space-x-3">
							<MapPin className="h-5 w-5 text-gray-400 mt-0.5" />
							<div className="flex-grow">
								<div className="flex items-center justify-between">
									<p className="font-medium text-gray-900">法人住所</p>
									<button
										type="button"
										onClick={() => toggleHistory("address")}
										disabled={
											data.company_info.current_address ===
											data.company_info.original_address
										}
										className={`text-sm flex items-center space-x-1 ${
											data.company_info.current_address ===
											data.company_info.original_address
												? "text-gray-400 cursor-not-allowed"
												: "text-blue-600 hover:text-blue-700"
										}`}
									>
										{showHistory.address ? (
											<EyeOff className="h-4 w-4" />
										) : (
											<Eye className="h-4 w-4" />
										)}
										<span>歷史</span>
									</button>
								</div>
								<p className="text-gray-600 text-sm mt-1">{info.address}</p>

								{showHistory.address && (
									<div className="mt-3 p-3 bg-gray-50 rounded-lg">
										<h4 className="font-medium text-gray-700 mb-2">
											住所變更歷史
										</h4>
										{data.history
											.filter((h) => h.type.includes("住所"))
											.map((h) => (
												<div
													key={h.event_id}
													className="text-sm text-gray-600 mb-2"
												>
													<p className="font-medium">{h.date}</p>
													{"old_address" in h.details && (
														<p>
															從:{" "}
															{String(
																(h.details as { old_address?: string })
																	.old_address || "",
															)}
														</p>
													)}
													{"new_address" in h.details && (
														<p>
															到:{" "}
															{String(
																(h.details as { new_address?: string })
																	.new_address || "",
															)}
														</p>
													)}
												</div>
											))}
									</div>
								)}
							</div>
						</div>

						<div className="flex items-start space-x-3">
							<Briefcase className="h-5 w-5 text-gray-400 mt-0.5" />
							<div className="flex-grow">
								<div className="flex items-center justify-between">
									<p className="font-medium text-gray-900">所營事業</p>
									<button
										type="button"
										onClick={() => toggleHistory("business")}
										className="text-blue-600 hover:text-blue-700 text-sm flex items-center space-x-1"
									>
										{showHistory.business ? (
											<EyeOff className="h-4 w-4" />
										) : (
											<Eye className="h-4 w-4" />
										)}
										<span>歷史</span>
									</button>
								</div>
								<p className="text-gray-600 text-sm mt-1">
									{info.businessScope}
								</p>

								{showHistory.business && (
									<div className="mt-3 p-3 bg-gray-50 rounded-lg">
										<h4 className="font-medium text-gray-700 mb-2">
											營業範圍變更歷史
										</h4>
										{data.history
											.filter((h) => h.type.includes("事業"))
											.map((h) => (
												<div
													key={h.event_id}
													className="text-sm text-gray-600 mb-2"
												>
													<p className="font-medium">{h.date}</p>
													{"old_business_scope" in h.details && (
														<p>
															從:{" "}
															{String(
																(h.details as { old_business_scope?: string })
																	.old_business_scope || "",
															)}
														</p>
													)}
													{"new_business_scope" in h.details && (
														<p>
															到:{" "}
															{String(
																(h.details as { new_business_scope?: string })
																	.new_business_scope || "",
															)}
														</p>
													)}
												</div>
											))}
									</div>
								)}
							</div>
						</div>

						<div className="flex items-start space-x-3">
							<DollarSign className="h-5 w-5 text-gray-400 mt-0.5" />
							<div className="flex-grow">
								<div className="flex items-center justify-between">
									<p className="font-medium text-gray-900">資本額</p>
									<button
										type="button"
										onClick={() => toggleHistory("capital")}
										className="text-blue-600 hover:text-blue-700 text-sm flex items-center space-x-1"
									>
										{showHistory.capital ? (
											<EyeOff className="h-4 w-4" />
										) : (
											<Eye className="h-4 w-4" />
										)}
										<span>歷史</span>
									</button>
								</div>
								<p className="text-gray-600 text-sm mt-1">
									{info.capital.toLocaleString()} {data.company_info.currency}
								</p>

								{showHistory.capital && (
									<div className="mt-3 p-3 bg-gray-50 rounded-lg">
										<h4 className="font-medium text-gray-700 mb-2">
											資本額變更歷史
										</h4>
										{data.history
											.filter(
												(h) =>
													h.type.includes("資") &&
													("old_capital" in h.details ||
														"capital" in h.details),
											)
											.map((h) => (
												<div
													key={h.event_id}
													className="text-sm text-gray-600 mb-2"
												>
													<p className="font-medium">
														{h.date} - {h.type}
													</p>
													{"old_capital" in h.details && (
														<p>
															從:{" "}
															{(
																(h.details as { old_capital?: number })
																	.old_capital || 0
															).toLocaleString()}{" "}
															到:{" "}
															{(
																(h.details as { new_capital?: number })
																	.new_capital || 0
															).toLocaleString()}
														</p>
													)}
													{"capital" in h.details &&
														!("old_capital" in h.details) && (
															<p>
																設立資本:{" "}
																{(
																	(h.details as { capital?: number }).capital ||
																	0
																).toLocaleString()}
															</p>
														)}
												</div>
											))}
									</div>
								)}
							</div>
						</div>
					</div>
				</div>

				{/* Administrative Management */}
				<div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
					<h3 className="text-lg font-semibold text-gray-900 mb-4">
						行政管理成員
					</h3>
					<div className="space-y-4">
						{data.current_administrators.map((admin) => (
							<div
								key={`${admin.name}-${admin.appointment_date}`}
								className="border border-gray-200 rounded-lg p-4"
							>
								<div className="flex items-center space-x-3 mb-2">
									<Users className="h-5 w-5 text-gray-400" />
									<div>
										<h4 className="font-medium text-gray-900">
											<ClickableShareholderName shareholderName={admin.name}>
												{admin.name}
											</ClickableShareholderName>
										</h4>
										<p className="text-sm text-gray-600">{admin.title}</p>
									</div>
								</div>
								<div className="text-sm text-gray-600">
									<p>簽名方式: {admin.signature_method}</p>
									<p>任職日期: {admin.appointment_date}</p>
								</div>
							</div>
						))}
					</div>

					<div className="mt-4">
						<button
							type="button"
							onClick={() => toggleHistory("admin")}
							className="text-blue-600 hover:text-blue-700 text-sm flex items-center space-x-1"
						>
							{showHistory.admin ? (
								<EyeOff className="h-4 w-4" />
							) : (
								<Eye className="h-4 w-4" />
							)}
							<span>查看歷史管理成員</span>
						</button>

						{showHistory.admin && (
							<div className="mt-3 p-3 bg-gray-50 rounded-lg">
								<h4 className="font-medium text-gray-700 mb-2">歷史管理成員</h4>
								{data.administrator_history &&
								data.administrator_history.length > 0 ? (
									data.administrator_history.map((h, index: number) => (
										<div
											key={`${h.date}-${index}`}
											className="text-sm text-gray-600 mb-2 border-b border-gray-200 pb-2"
										>
											<p className="font-medium">
												{h.date} - {h.type}
											</p>
											<p className="text-gray-500">{h.details?.action || ""}</p>
											{h.details?.administrators &&
												Array.isArray(h.details.administrators) &&
												h.details.administrators.length > 0 && (
													<p>
														管理員:{" "}
														{h.details.administrators.map(
															(adminName: string, index: number) => (
																<span key={adminName}>
																	<ClickableShareholderName
																		shareholderName={adminName}
																	>
																		{adminName}
																	</ClickableShareholderName>
																	{index <
																		(h.details.administrators?.length || 0) -
																			1 && ", "}
																</span>
															),
														)}
													</p>
												)}
										</div>
									))
								) : (
									<p className="text-sm text-gray-500">暫無歷史管理成員記錄</p>
								)}
							</div>
						)}
					</div>
				</div>
			</div>
		</div>
	);
}
