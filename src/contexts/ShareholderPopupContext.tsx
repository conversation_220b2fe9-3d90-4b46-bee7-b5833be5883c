import type React from "react";
import { type ReactNode, createContext, useContext, useState } from "react";
import type { ShareholderInfo } from "../types/business-registration-types";

interface ShareholderPopupContextType {
	showPopup: (info: ShareholderInfo, x: number, y: number) => void;
	hidePopup: () => void;
	popupInfo: ShareholderInfo | null;
	position: { x: number; y: number };
	isVisible: boolean;
}

const ShareholderPopupContext = createContext<
	ShareholderPopupContextType | undefined
>(undefined);

export const useShareholderPopup = () => {
	const context = useContext(ShareholderPopupContext);
	if (!context) {
		throw new Error(
			"useShareholderPopup must be used within a ShareholderPopupProvider",
		);
	}
	return context;
};

interface ShareholderPopupProviderProps {
	children: ReactNode;
}

export const ShareholderPopupProvider: React.FC<
	ShareholderPopupProviderProps
> = ({ children }) => {
	const [popupInfo, setPopupInfo] = useState<ShareholderInfo | null>(null);
	const [position, setPosition] = useState({ x: 0, y: 0 });
	const [isVisible, setIsVisible] = useState(false);

	const showPopup = (info: ShareholderInfo, x: number, y: number) => {
		setPopupInfo(info);
		setPosition({ x, y });
		setIsVisible(true);
	};

	const hidePopup = () => {
		setIsVisible(false);
		setTimeout(() => {
			setPopupInfo(null);
		}, 150); // Small delay for smooth fade out
	};

	return (
		<ShareholderPopupContext.Provider
			value={{
				showPopup,
				hidePopup,
				popupInfo,
				position,
				isVisible,
			}}
		>
			{children}
		</ShareholderPopupContext.Provider>
	);
};
