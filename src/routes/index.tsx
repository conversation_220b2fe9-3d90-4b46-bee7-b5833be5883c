import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { ArrowRight, Building, FileText, Settings, Upload } from "lucide-react";
import { useState } from "react";
import { PdfUpload } from "../components/PdfUpload";
import { SettingsPanel } from "../components/SettingsPanel";
import { apiKeyManager } from "../services/api-key-manager";
import type { ProcessingResult } from "../services/pdf-processor";
import type { BusinessRegistrationReport } from "../types/business-registration-types";

export const Route = createFileRoute("/")({
	component: LandingPage,
});

function LandingPage() {
	const [showSettings, setShowSettings] = useState(false);
	const [, setProcessedData] = useState<BusinessRegistrationReport | null>(
		null,
	);
	const navigate = useNavigate();

	const handleProcessingComplete = (result: ProcessingResult) => {
		if (result.success && result.data) {
			setProcessedData(result.data);
			// Navigate to dashboard with the processed data
			// For now, we'll store it temporarily and navigate
			sessionStorage.setItem(
				"processedBusinessData",
				JSON.stringify(result.data),
			);
			navigate({ to: "/dashboard" });
		}
	};

	const handleSettingsClick = () => {
		setShowSettings(true);
	};

	return (
		<div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-emerald-50 flex flex-col items-center justify-center p-6">
			<div className="max-w-4xl w-full text-center space-y-8">
				{/* Header with Settings */}
				<div className="space-y-4">
					<div className="flex items-center justify-between">
						<div className="flex-1" />
						<div className="flex items-center space-x-3 mb-6">
							<Building className="h-10 w-10 text-blue-600" />
							<h1 className="text-4xl font-bold text-gray-900">
								澳門商業登記報告
							</h1>
						</div>
						<div className="flex-1 flex justify-end">
							<button
								type="button"
								onClick={handleSettingsClick}
								className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
								title="設定"
							>
								<Settings className="h-6 w-6" />
							</button>
						</div>
					</div>
					<h2 className="text-2xl font-semibold text-gray-700">
						數位化儀表板 Demo
					</h2>
					<p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
						將傳統商業登記PDF報告轉化為直觀、互動的數位化儀表板，
						快速擷取公司概況、股東變動及歷史資訊
					</p>
				</div>

				{/* API Key Setup Notice */}
				{!apiKeyManager.hasApiKey() && (
					<div className="max-w-md mx-auto mb-6">
						<div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
							<p className="text-blue-800 text-sm">
								<strong>首次使用：</strong>請先點擊右上角設定按鈕配置您的 OpenRouter
								API 金鑰
							</p>
						</div>
					</div>
				)}

				{/* Upload Area */}
				<PdfUpload
					onProcessingComplete={handleProcessingComplete}
					className={
						!apiKeyManager.hasApiKey() ? "opacity-50 pointer-events-none" : ""
					}
				/>

				{/* Demo Button */}
				<div className="pt-8">
					<button
						type="button"
						onClick={() => navigate({ to: "/dashboard" })}
						className="inline-flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
					>
						<span>查看範例儀表板</span>
						<ArrowRight className="h-5 w-5" />
					</button>
				</div>

				{/* Demo Preview */}
				<div className="bg-white rounded-2xl shadow-lg p-8 mb-12">
					<h3 className="text-xl font-bold text-gray-900 mb-6 text-center">
						從紙本報告到數位儀表板
					</h3>
					<div className="grid md:grid-cols-2 gap-8 items-center">
						<div className="text-center">
							<div className="bg-gray-100 rounded-lg p-6 mb-4">
								<FileText className="h-16 w-16 text-gray-400 mx-auto mb-2" />
								<p className="text-gray-600 font-medium">傳統PDF報告</p>
								<p className="text-sm text-gray-500 mt-2">
									• 15頁密集文字
									<br />• 15項歷史事件
									<br />• 5位股東變動
									<br />• 6次資本變更
								</p>
							</div>
						</div>
						<div className="text-center">
							<div className="bg-gradient-to-br from-blue-50 to-emerald-50 rounded-lg p-6 mb-4">
								<Building className="h-16 w-16 text-blue-600 mx-auto mb-2" />
								<p className="text-gray-900 font-medium">智能儀表板</p>
								<p className="text-sm text-emerald-600 mt-2">
									• 8個關鍵指標卡片
									<br />• 股權結構圓餅圖
									<br />• 互動式歷史時間軸
									<br />• 一鍵查看詳細資料
								</p>
							</div>
						</div>
					</div>
				</div>

				{/* Features */}
				<div className="grid md:grid-cols-4 gap-6 pt-8">
					<div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow group">
						<FileText className="h-8 w-8 text-blue-600 mb-4 group-hover:scale-110 transition-transform" />
						<h3 className="font-semibold text-gray-900 mb-2">AI智能解析</h3>
						<p className="text-gray-600 text-sm">
							自動識別並提取PDF中的公司資訊、股東結構、歷史變更記錄
						</p>
					</div>
					<div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow group">
						<Building className="h-8 w-8 text-emerald-600 mb-4 group-hover:scale-110 transition-transform" />
						<h3 className="font-semibold text-gray-900 mb-2">視覺化呈現</h3>
						<p className="text-gray-600 text-sm">
							複雜數據轉化為直觀圖表，股權分布一目了然
						</p>
					</div>
					<div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow group">
						<ArrowRight className="h-8 w-8 text-orange-600 mb-4 group-hover:scale-110 transition-transform" />
						<h3 className="font-semibold text-gray-900 mb-2">互動式探索</h3>
						<p className="text-gray-600 text-sm">
							點擊展開歷史記錄，追蹤公司發展軌跡和股權變化
						</p>
					</div>
					<div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow group">
						<Upload className="h-8 w-8 text-purple-600 mb-4 group-hover:scale-110 transition-transform" />
						<h3 className="font-semibold text-gray-900 mb-2">秒級處理</h3>
						<p className="text-gray-600 text-sm">
							上傳PDF後即時生成完整分析報告，大幅提升工作效率
						</p>
					</div>
				</div>
			</div>

			{/* Settings Panel */}
			<SettingsPanel
				isOpen={showSettings}
				onClose={() => setShowSettings(false)}
			/>
		</div>
	);
}
