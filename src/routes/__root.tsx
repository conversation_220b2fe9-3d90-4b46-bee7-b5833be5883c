import { Outlet, createRootRoute } from "@tanstack/react-router";

import Header from "../components/Header";
import { ShareholderPopup } from "../components/ShareholderPopup";
import { ShareholderPopupProvider } from "../contexts/ShareholderPopupContext";

export const Route = createRootRoute({
	component: () => (
		<ShareholderPopupProvider>
			<Header />
			<Outlet />
			<ShareholderPopup />
			{/* <TanStackRouterDevtools /> */}
		</ShareholderPopupProvider>
	),
});
